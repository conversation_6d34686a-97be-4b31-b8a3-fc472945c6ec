import torch
import torch.nn as nn
import torch.nn.functional as F


class VoxelAugmentationContrastiveLoss(nn.Module):
    """
    简洁的体素数据增强对比学习损失
    """
    
    def __init__(self, temperature=0.07, noise_scale=0.1):
        super().__init__()
        self.temperature = temperature
        self.noise_scale = noise_scale
        
    def forward(self, original_prompts):
        """
        计算对比学习损失
        
        Args:
            original_prompts: [B, N, D] 原始体素prompt
            
        Returns:
            contrastive_loss: 对比学习损失
        """
        B, N, D = original_prompts.shape
        
        # 1. 生成增强prompt (简单添加噪声)
        noise = torch.randn_like(original_prompts) * self.noise_scale
        augmented_prompts = original_prompts + noise
        
        # 2. 计算全局表征
        original_global = F.normalize(original_prompts.mean(dim=1), dim=1)    # [B, D]
        augmented_global = F.normalize(augmented_prompts.mean(dim=1), dim=1)  # [B, D]
        
        # 3. 正样本相似度 (同一样本的原始vs增强)
        pos_similarity = torch.sum(original_global * augmented_global, dim=1) / self.temperature  # [B]
        
        # 4. 负样本相似度 (batch内其他样本)
        neg_similarity_matrix = torch.matmul(original_global, original_global.T) / self.temperature  # [B, B]
        mask = torch.eye(B, device=original_prompts.device).bool()
        neg_similarity_matrix = neg_similarity_matrix.masked_fill(mask, float('-inf'))
        neg_similarity = neg_similarity_matrix.max(dim=1)[0]  # [B]
        
        # 5. InfoNCE损失
        pos_exp = torch.exp(pos_similarity)
        neg_exp = torch.exp(neg_similarity)
        loss = -torch.log(pos_exp / (pos_exp + neg_exp + 1e-8)).mean()
        
        return loss
