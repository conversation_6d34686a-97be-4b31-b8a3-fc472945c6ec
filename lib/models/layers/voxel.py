import torch
import torch.nn as nn
import torch.nn.functional as F
from lib.models.layers.voxel_motion_extractor import DenseVoxelEncoder, VoxelMotionExtractor

def cka_loss(X, Y):
    """计算中心化核对齐（CKA）损失"""
    # 1. 计算 Gram 矩阵
    X_gram = X @ X.T
    Y_gram = Y @ Y.T

    # 2. 中心化 Gram 矩阵
    n = X_gram.shape[0]
    H = torch.eye(n, device=X.device) - 1.0 / n
    X_gram_centered = H @ X_gram @ H
    Y_gram_centered = H @ Y_gram @ H

    # 3. 计算 CKA 指数 (HSIC / sqrt(HSIC_X * HSIC_Y))
    hsic = (X_gram_centered * Y_gram_centered).sum()
    var_x = (X_gram_centered * X_gram_centered).sum()
    var_y = (Y_gram_centered * Y_gram_centered).sum()

    # CKA 相似度范围 [0, 1]，我们希望最大化它，所以损失为 1 - cka
    cka_similarity = hsic / (torch.sqrt(var_x * var_y) + 1e-8)
    
    return 1.0 - cka_similarity

# 优化后的 TemplateGuidedAttention
class TemplateGuidedAttention(nn.Module):
    def __init__(self, embed_dim=768):
        super().__init__()
        # 轻量级门控网络
        self.gating_net = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 4),
            nn.ReLU(inplace=True),
            nn.Linear(embed_dim // 4, 1), # 每个token输出一个门控标量
            nn.Sigmoid()
        )
        self.norm = nn.LayerNorm(embed_dim)

    def forward(self, template_features, voxel_features):
        # 1. 获取模板原型作为动态查询
        template_prototype = template_features.mean(dim=1, keepdim=True) # [B, 1, C]

        # 2. 计算每个体素token与模板原型的相似度特征
        #    这里使用简单的相加或相乘作为输入
        gate_input = voxel_features + template_prototype # [B, L_v, C]

        # 3. 预测门控权重
        gate = self.gating_net(gate_input) # [B, L_v, 1]

        # 4. 应用门控并进行归一化
        motion_features = self.norm(voxel_features * gate)

        return motion_features

class CrossAttentionFusion(nn.Module):
    def __init__(self, embed_dim=768, num_heads=8):
        super().__init__()
        self.norm = nn.LayerNorm(embed_dim)

        self.cross_attn = nn.MultiheadAttention(
            embed_dim=embed_dim,
            num_heads=num_heads,
            batch_first=True
        )
        
        self.fusion = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.GELU(),
            nn.Dropout(0.1)
        )
        
        # 动态权重预测 - 基于运动特性
        self.alpha_predictor = nn.Sequential(
            nn.Linear(embed_dim * 2, 128),
            nn.ReLU(),
            nn.Linear(128, 1),
            nn.Sigmoid()
        )
        
        self.base_alpha = 0
        self.alpha_scale = 1
    
    def forward(self, search_features, motion_features):
        B, L_s, C = search_features.shape
        B, L_m, C = motion_features.shape

        input_features = search_features.clone()

        search_features = self.norm(search_features)
        motion_features = self.norm(motion_features)
        
        # 跨注意力计算
        motion_context, attn_weights = self.cross_attn(
            query=search_features,
            key=motion_features,
            value=motion_features
        )
        
        # 评估运动特性
        prediction_input = torch.cat([search_features, motion_context], dim=-1)  # [B, L_s, 2C]
        dynamic_factor = self.alpha_predictor(prediction_input)  # [B, L_s, 1]
        
        # 运动强度越高，融合权重越大
        alpha = self.base_alpha + self.alpha_scale * dynamic_factor  # [B, L_s, 1]
        
        # 应用融合
        fusion_motion = self.fusion(motion_context)
        enhanced_features = search_features + alpha * fusion_motion
        '''
        # --- 辅助损失计算 ---
        input_norm = F.normalize(input_features, p=2, dim=-1)
        output_norm = F.normalize(enhanced_features, p=2, dim=-1)
        similarity = (input_norm * output_norm).sum(dim=-1)
        ortho_loss = similarity.abs().mean()
        
        # --- 辅助损失计算 (方案一：余弦相似度一致性损失) ---
        # 目标：让原始视觉特征与对齐后的运动特征尽可能相似
        input_norm = F.normalize(input_features, p=2, dim=-1)
        motion_norm = F.normalize(motion_context, p=2, dim=-1)
        
        # 计算余弦相似度
        cosine_sim = (input_norm * motion_norm).sum(dim=-1)
        ortho_loss = (1.0 - cosine_sim).mean()
        '''
        # --- 辅助损失计算 (CKA 损失) ---
        # 目标：让融合后的特征与原始视觉特征在表征结构上保持一致
        # CKA 需要 (N, D) 输入, 我们将 Batch 和 Token 维度合并
        input_flat = input_features.reshape(B * L_s, C).contiguous()
        context_flat = motion_context.reshape(B * L_s, C).contiguous()
        
        # 计算损失
        ortho_loss = cka_loss(input_flat, context_flat)

        return enhanced_features, ortho_loss, motion_context, {
            "attn_weights": attn_weights,
        }
'''  
class CrossAttentionFusion(nn.Module):
    def __init__(self, embed_dim=768, num_heads=8):
        super().__init__()
        self.norm = nn.LayerNorm(embed_dim)

        self.cross_attn = nn.MultiheadAttention(
            embed_dim=embed_dim,
            num_heads=num_heads,
            batch_first=True
        )
        
        self.fusion = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.GELU(),
            nn.Dropout(0.1)
        )
        
        # 动态权重预测 - 基于运动特性
        self.alpha_predictor = nn.Sequential(
            nn.Linear(embed_dim, 64),  # +2 for motion metrics
            nn.ReLU(),
            nn.Linear(64, 1),
            nn.Sigmoid()
        )
        
        self.base_alpha = 0.2
        self.alpha_scale = 0.8
    
    def forward(self, search_features, motion_features):
        B, L_s, C = search_features.shape
        B, L_m, C = motion_features.shape

        search_features = self.norm(search_features)
        motion_features = self.norm(motion_features)
        
        # 跨注意力计算
        motion_context, attn_weights = self.cross_attn(
            query=search_features,
            key=motion_features,
            value=motion_features
        )
        
        # 评估运动特性
        motion_global = motion_features.mean(dim=1)  # [B, C]
        dynamic_factor = self.alpha_predictor(motion_global)  # [B, 1]
        
        # 运动强度越高，融合权重越大
        alpha = self.base_alpha + self.alpha_scale * dynamic_factor  # [B, 1]
        
        # 应用融合
        alpha = alpha.unsqueeze(1)  # [B, 1, 1]
        enhanced_features = search_features + alpha * self.fusion(motion_context)
        
        return enhanced_features, motion_context, {
            "attn_weights": attn_weights,
        }
'''
# 组合模块
class VoxelMotionEnhancer(nn.Module):
    def __init__(self, embed_dim=768, num_heads=8):
        super().__init__()
        self.voxel_encoder = DenseVoxelEncoder(embed_dim=embed_dim)
        self.motion_extractor = VoxelMotionExtractor(motion_channels=embed_dim)
        self.projector = nn.Sequential(
            nn.Linear(256, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.Dropout(0.1)
        )

        self.cross_attn_fusion = CrossAttentionFusion(embed_dim=embed_dim, num_heads=num_heads)
    
    def forward(self, search_features, voxel_data):
        """
        端到端的体素运动增强
        """
        # 体素特征提取
        # voxel_features = self.voxel_encoder(voxel_data)
        voxel_features, _ = self.motion_extractor(voxel_data)
        voxel_features = self.projector(voxel_features)
        
        # 跨注意力融合
        enhanced_features, motion_attn = self.cross_attn_fusion(search_features, voxel_features)
        
        return enhanced_features, {
            "motion_attn": motion_attn
        }

class VoxelMotionEnhancer(nn.Module):
    def __init__(self, embed_dim=768, num_heads=8):
        super().__init__()
        self.motion_extractor = VoxelMotionExtractor(motion_channels=embed_dim)
        self.projector = nn.Sequential(
            nn.Linear(256, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.Dropout(0.1)
        )

        self.cross_attn_fusion = CrossAttentionFusion(embed_dim=embed_dim, num_heads=num_heads)
    
    def forward(self, search_features, coords, features, spatial_shape, empty_batch_mask):
        """
        端到端的体素运动增强
        """
        # 体素特征提取
        # voxel_features = self.voxel_encoder(voxel_data)
        voxel_features, _ = self.motion_extractor(voxel_data)
        voxel_features = self.projector(voxel_features)
        
        # 跨注意力融合
        enhanced_features, motion_attn = self.cross_attn_fusion(search_features, voxel_features)
        
        return enhanced_features, {
            "motion_attn": motion_attn
        }