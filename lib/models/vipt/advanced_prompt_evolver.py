import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class LayerAdaptiveEvolver(nn.Module):
    """层级自适应演化器 - 根据网络深度调整演化策略"""
    def __init__(self, dim, layer_idx, total_layers):
        super().__init__()
        self.layer_idx = layer_idx
        self.total_layers = total_layers
        
        # 层级感知的演化强度
        self.evolution_strength = nn.Parameter(
            torch.tensor(float(layer_idx + 1) / total_layers)
        )
        
        # 浅层：注重细节保持，深层：注重语义融合
        if layer_idx < total_layers // 3:  # 浅层
            self.evolution_type = "detail_preserving"
            self.local_attn = nn.MultiheadAttention(dim, num_heads=4, batch_first=True)
        elif layer_idx < 2 * total_layers // 3:  # 中层
            self.evolution_type = "semantic_fusion"  
            self.semantic_attn = nn.MultiheadAttention(dim, num_heads=8, batch_first=True)
        else:  # 深层
            self.evolution_type = "global_reasoning"
            self.global_attn = nn.MultiheadAttention(dim, num_heads=12, batch_first=True)
            
        # 演化门控
        self.evolution_gate = nn.Sequential(
            nn.Linear(dim, dim // 4),
            nn.GELU(),
            nn.Linear(dim // 4, 1),
            nn.Sigmoid()
        )
        
    def forward(self, prompt, backbone_features):
        gate_weight = self.evolution_gate(backbone_features.mean(dim=1, keepdim=True))
        
        if self.evolution_type == "detail_preserving":
            # 浅层：细节保持式演化
            evolved, _ = self.local_attn(prompt, backbone_features, backbone_features)
        elif self.evolution_type == "semantic_fusion":
            # 中层：语义融合式演化
            evolved, _ = self.semantic_attn(prompt, backbone_features, backbone_features)
        else:
            # 深层：全局推理式演化
            evolved, _ = self.global_attn(prompt, backbone_features, backbone_features)
            
        return prompt + self.evolution_strength * gate_weight * evolved

class CrossModalKnowledgeDistillation(nn.Module):
    """跨模态知识蒸馏 - 让两种提示相互学习"""
    def __init__(self, dim):
        super().__init__()
        self.img_to_motion_proj = nn.Linear(dim, dim)
        self.motion_to_img_proj = nn.Linear(dim, dim)
        
        # 知识蒸馏权重
        self.distill_weight = nn.Parameter(torch.tensor(0.1))
        
    def forward(self, img_prompt, motion_prompt):
        if motion_prompt is None:
            return img_prompt, None
            
        # 图像提示学习运动信息
        motion_knowledge = self.motion_to_img_proj(motion_prompt.mean(dim=1, keepdim=True))
        enhanced_img = img_prompt + self.distill_weight * motion_knowledge
        
        # 运动提示学习空间信息  
        spatial_knowledge = self.img_to_motion_proj(img_prompt.mean(dim=1, keepdim=True))
        enhanced_motion = motion_prompt + self.distill_weight * spatial_knowledge
        
        return enhanced_img, enhanced_motion

class MemoryEnhancedEvolution(nn.Module):
    """记忆增强演化 - 维护历史演化经验"""
    def __init__(self, dim, memory_size=64):
        super().__init__()
        self.memory_size = memory_size
        
        # 可学习的记忆库
        self.register_buffer('img_memory', torch.randn(memory_size, dim))
        self.register_buffer('motion_memory', torch.randn(memory_size, dim))
        
        # 记忆检索
        self.memory_attn = nn.MultiheadAttention(dim, num_heads=4, batch_first=True)
        
        # 记忆更新门控
        self.update_gate = nn.Linear(dim, 1)
        
    def forward(self, prompt, prompt_type="image"):
        memory = self.img_memory if prompt_type == "image" else self.motion_memory
        
        # 从记忆中检索相关经验
        memory_expanded = memory.unsqueeze(0).expand(prompt.size(0), -1, -1)
        retrieved_memory, _ = self.memory_attn(prompt, memory_expanded, memory_expanded)
        
        # 结合历史经验
        enhanced_prompt = prompt + 0.1 * retrieved_memory
        
        # 更新记忆 (训练时)
        if self.training:
            update_weight = torch.sigmoid(self.update_gate(prompt.mean(dim=1)))
            avg_prompt = prompt.mean(dim=0)  # [memory_size, dim]
            
            if prompt_type == "image":
                self.img_memory.data = (0.99 * self.img_memory.data + 
                                      0.01 * update_weight.mean() * avg_prompt.data)
            else:
                self.motion_memory.data = (0.99 * self.motion_memory.data + 
                                         0.01 * update_weight.mean() * avg_prompt.data)
        
        return enhanced_prompt

class AdvancedPromptEvolver(nn.Module):
    """高级提示演化器 - 集成多种演化机制"""
    def __init__(self, dim, num_heads, layer_idx=0, total_layers=12):
        super().__init__()
        
        # 1. 层级自适应演化
        self.layer_adaptive_img = LayerAdaptiveEvolver(dim, layer_idx, total_layers)
        self.layer_adaptive_motion = LayerAdaptiveEvolver(dim, layer_idx, total_layers)
        
        # 2. 跨模态知识蒸馏
        self.cross_modal_distill = CrossModalKnowledgeDistillation(dim)
        
        # 3. 记忆增强机制
        self.memory_enhanced = MemoryEnhancedEvolution(dim)
        
        # 4. 标准化层
        self.norm_x = nn.LayerNorm(dim)
        self.norm_img = nn.LayerNorm(dim)
        self.norm_motion = nn.LayerNorm(dim)
        
        # 5. 演化强度控制
        self.evolution_controller = nn.Sequential(
            nn.Linear(dim, dim // 4),
            nn.GELU(), 
            nn.Linear(dim // 4, 2),
            nn.Softmax(dim=-1)
        )
        
    def forward(self, x, img_prompt, motion_prompt=None):
        """
        高级演化机制
        Args:
            x: 主干特征 [B, N, C]
            img_prompt: 图像提示 [B, N_img, C]
            motion_prompt: 运动提示 [B, N_motion, C] (可选)
        """
        x_norm = self.norm_x(x)
        img_norm = self.norm_img(img_prompt)
        
        # 1. 跨模态知识蒸馏
        if motion_prompt is not None:
            motion_norm = self.norm_motion(motion_prompt)
            img_enhanced, motion_enhanced = self.cross_modal_distill(img_norm, motion_norm)
        else:
            img_enhanced = img_norm
            motion_enhanced = None
            
        # 2. 层级自适应演化
        img_evolved = self.layer_adaptive_img(img_enhanced, x_norm)
        
        motion_evolved = None
        if motion_enhanced is not None:
            motion_evolved = self.layer_adaptive_motion(motion_enhanced, x_norm)
            
        # 3. 记忆增强
        img_final = self.memory_enhanced(img_evolved, "image")
        
        motion_final = None
        if motion_evolved is not None:
            motion_final = self.memory_enhanced(motion_evolved, "motion")
            
        return img_final, motion_final
