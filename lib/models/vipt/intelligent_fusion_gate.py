import torch
import torch.nn as nn
import torch.nn.functional as F

class SpatialContextEncoder(nn.Module):
    """空间上下文编码器"""
    def __init__(self, dim):
        super().__init__()
        self.spatial_conv = nn.Sequential(
            nn.Conv2d(dim, dim//2, 3, padding=1),
            nn.BatchNorm2d(dim//2),
            nn.ReLU(),
            nn.Conv2d(dim//2, dim//4, 3, padding=1),
            nn.AdaptiveAvgPool2d(1)
        )
        
    def forward(self, x_feat):
        """x_feat: [B, C, H, W]"""
        return self.spatial_conv(x_feat).squeeze(-1).squeeze(-1)  # [B, C//4]

class TemporalContextEncoder(nn.Module):
    """时序上下文编码器"""
    def __init__(self, dim):
        super().__init__()
        self.temporal_proj = nn.Linear(dim, dim//4)
        self.temporal_attn = nn.MultiheadAttention(dim//4, num_heads=4, batch_first=True)
        
    def forward(self, motion_features):
        """motion_features: [B, N, C]"""
        if motion_features is None:
            return None
        projected = self.temporal_proj(motion_features)
        attended, _ = self.temporal_attn(projected, projected, projected)
        return attended.mean(dim=1)  # [B, C//4]

class UncertaintyEstimator(nn.Module):
    """不确定性估计器"""
    def __init__(self, dim):
        super().__init__()
        self.uncertainty_net = nn.Sequential(
            nn.Linear(dim, dim//2),
            nn.ReLU(),
            nn.Linear(dim//2, 2),  # 为两种提示估计不确定性
            nn.Softplus()  # 确保输出为正
        )
        
    def forward(self, context_features):
        """基于上下文特征估计不确定性"""
        return self.uncertainty_net(context_features)

class DynamicWeightGenerator(nn.Module):
    """动态权重生成器"""
    def __init__(self, dim):
        super().__init__()
        self.context_dim = dim//4 + dim//4 + dim//4  # spatial + temporal + semantic
        
        self.weight_net = nn.Sequential(
            nn.Linear(self.context_dim, dim//2),
            nn.LayerNorm(dim//2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(dim//2, dim//4),
            nn.GELU(),
            nn.Linear(dim//4, 2),
            nn.Softmax(dim=-1)
        )
        
        # 权重平滑机制
        self.weight_smoother = nn.Parameter(torch.tensor(0.1))
        self.register_buffer('prev_weights', torch.tensor([0.5, 0.5]))
        
    def forward(self, spatial_ctx, temporal_ctx, semantic_ctx):
        # 处理temporal_ctx为None的情况
        if temporal_ctx is None:
            temporal_ctx = torch.zeros_like(spatial_ctx[:, :spatial_ctx.size(1)//2])
            
        # 拼接所有上下文
        context_features = torch.cat([spatial_ctx, temporal_ctx, semantic_ctx], dim=-1)
        
        # 生成权重
        current_weights = self.weight_net(context_features)
        
        # 时序平滑
        if self.training:
            smoothed_weights = (1 - self.weight_smoother) * current_weights + \
                             self.weight_smoother * self.prev_weights.unsqueeze(0)
            self.prev_weights.data = current_weights.mean(dim=0).data
        else:
            smoothed_weights = current_weights
            
        return smoothed_weights

class IntelligentFusionGate(nn.Module):
    """智能融合门控 - 基于多维上下文的动态融合"""
    def __init__(self, dim):
        super().__init__()
        
        # 1. 多层次上下文编码器
        self.spatial_encoder = SpatialContextEncoder(dim)
        self.temporal_encoder = TemporalContextEncoder(dim)
        
        # 2. 语义上下文编码器 (基于RGB特征)
        self.semantic_encoder = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(dim, dim//4),
            nn.ReLU()
        )
        
        # 3. 不确定性估计
        self.uncertainty_estimator = UncertaintyEstimator(dim//4)
        
        # 4. 动态权重生成
        self.weight_generator = DynamicWeightGenerator(dim)
        
        # 5. 自适应融合
        self.fusion_proj = nn.Linear(dim, dim)
        self.fusion_norm = nn.LayerNorm(dim)
        
    def forward(self, x_feat, img_prompt_feat, voxel_prompt_feat=None):
        """
        智能融合门控
        Args:
            x_feat: RGB特征图 [B, C, H, W]
            img_prompt_feat: 图像提示特征图 [B, C, H, W] 
            voxel_prompt_feat: 体素提示特征图 [B, C, H, W] (可选)
        """
        # 1. 提取多维上下文
        spatial_ctx = self.spatial_encoder(x_feat)
        temporal_ctx = None
        if voxel_prompt_feat is not None:
            # 将voxel特征转换为token形式进行时序建模
            B, C, H, W = voxel_prompt_feat.shape
            voxel_tokens = voxel_prompt_feat.flatten(2).transpose(1, 2)  # [B, HW, C]
            temporal_ctx = self.temporal_encoder(voxel_tokens)
        
        semantic_ctx = self.semantic_encoder(x_feat)
        
        # 2. 估计不确定性
        uncertainty = self.uncertainty_estimator(semantic_ctx)
        
        # 3. 生成动态权重
        fusion_weights = self.weight_generator(spatial_ctx, temporal_ctx, semantic_ctx)
        
        # 4. 不确定性加权融合
        if voxel_prompt_feat is not None:
            # 基于不确定性调整权重
            adjusted_weights = fusion_weights / (uncertainty + 1e-8)
            adjusted_weights = F.softmax(adjusted_weights, dim=-1)
            
            fused_features = (adjusted_weights[:, 0:1].unsqueeze(-1).unsqueeze(-1) * img_prompt_feat + 
                            adjusted_weights[:, 1:2].unsqueeze(-1).unsqueeze(-1) * voxel_prompt_feat)
        else:
            # 退化到单提示模式
            fused_features = img_prompt_feat
            
        # 5. 最终投影和归一化
        B, C, H, W = fused_features.shape
        fused_tokens = fused_features.flatten(2).transpose(1, 2)  # [B, HW, C]
        projected = self.fusion_proj(fused_tokens)
        normalized = self.fusion_norm(projected)
        
        # 转回特征图格式
        fused_output = normalized.transpose(1, 2).reshape(B, C, H, W)
        
        return fused_output, fusion_weights  # 返回融合结果和权重(用于可视化)

class AdvancedDualPromptBlock(nn.Module):
    """高级双提示块 - 集成智能融合机制"""
    def __init__(self, inplanes, hide_channel, smooth=False):
        super().__init__()
        
        # 原有的卷积投影
        self.conv0_0 = nn.Conv2d(inplanes, hide_channel, 1)  # RGB投影
        self.conv0_1 = nn.Conv2d(inplanes, hide_channel, 1)  # 图像提示投影  
        self.conv0_2 = nn.Conv2d(inplanes, hide_channel, 1)  # 体素提示投影
        
        # 智能融合门控
        self.intelligent_fusion = IntelligentFusionGate(hide_channel)
        
        # Fovea注意力
        self.fovea = Fovea(smooth=smooth)
        
        # 最终投影
        self.conv1x1 = nn.Conv2d(hide_channel, inplanes, 1)
        
        # 残差门控
        self.residual_gate = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(inplanes, inplanes//8, 1),
            nn.ReLU(),
            nn.Conv2d(inplanes//8, 1, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x, img_prompt, voxel_prompt=None):
        """
        Args:
            x: RGB特征 [B, C, H, W]
            img_prompt: 图像提示特征 [B, C, H, W]
            voxel_prompt: 体素提示特征 [B, C, H, W] (可选)
        """
        # 1. 投影到隐藏维度
        x_proj = self.conv0_0(x)
        img_proj = self.conv0_1(img_prompt)
        
        voxel_proj = None
        if voxel_prompt is not None:
            voxel_proj = self.conv0_2(voxel_prompt)
            
        # 2. 智能融合
        fused_prompt, fusion_weights = self.intelligent_fusion(x_proj, img_proj, voxel_proj)
        
        # 3. Fovea注意力增强
        enhanced_x = self.fovea(x_proj)
        
        # 4. 特征融合
        combined = enhanced_x + fused_prompt
        
        # 5. 最终投影
        output = self.conv1x1(combined)
        
        # 6. 自适应残差连接
        residual_weight = self.residual_gate(output)
        final_output = residual_weight * output + (1 - residual_weight) * x
        
        return final_output, fusion_weights
