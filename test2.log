nohup: 忽略输入
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00141_tank_outdoor2——————————————
00141_tank_outdoor2 , fps:8.484889328514756
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00147_tank_outdoor2——————————————
00147_tank_outdoor2 , fps:9.646183685827033
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00197_driving_outdoor3——————————————
00197_driving_outdoor3 , fps:7.043491867918245
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00236_tennis_outdoor4——————————————
00236_tennis_outdoor4 , fps:5.336823678966529
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00508_person_outdoor6——————————————
00508_person_outdoor6 , fps:9.047275074871628
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00510_person_outdoor6——————————————
00510_person_outdoor6 , fps:9.760369023084358
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00511_person_outdoor6——————————————
00511_person_outdoor6 , fps:9.432264453515778
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00514_person_outdoor6——————————————
00514_person_outdoor6 , fps:2.2440199946613464
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00421_UAV_outdoor6——————————————
00421_UAV_outdoor6 , fps:9.20783334628668
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00423_UAV_outdoor6——————————————
00423_UAV_outdoor6 , fps:11.79871788925561
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00425_UAV_outdoor6——————————————
00425_UAV_outdoor6 , fps:6.262805818207074
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00430_UAV_outdoor6——————————————
00430_UAV_outdoor6 , fps:5.916560399206325
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00449_UAV_outdoor6——————————————
00449_UAV_outdoor6 , fps:10.879286736794771
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00451_UAV_outdoor6——————————————
00451_UAV_outdoor6 , fps:10.79626625333626
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00453_UAV_outdoor6——————————————
00453_UAV_outdoor6 , fps:9.643317082861389
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00458_UAV_outdoor6——————————————
00458_UAV_outdoor6 , fps:1.8094450135970426
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00435_UAV_outdoor6——————————————
00435_UAV_outdoor6 , fps:9.12943891643835
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00437_UAV_outdoor6——————————————
00437_UAV_outdoor6 , fps:11.212378876448804
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00439_UAV_outdoor6——————————————
00439_UAV_outdoor6 , fps:6.223556000932585
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00442_UAV_outdoor6——————————————
00442_UAV_outdoor6 , fps:6.334885038491899
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00345_UAV_outdoor6——————————————
00345_UAV_outdoor6 , fps:9.920062965025016
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00351_UAV_outdoor6——————————————
00351_UAV_outdoor6 , fps:9.994762693800224
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00355_UAV_outdoor6——————————————
00355_UAV_outdoor6 , fps:6.436677137553631
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00370_UAV_outdoor6——————————————
00370_UAV_outdoor6 , fps:4.441688187344096
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00404_UAV_outdoor6——————————————
00404_UAV_outdoor6 , fps:9.236009944324584
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00406_UAV_outdoor6——————————————
00406_UAV_outdoor6 , fps:10.825580670519852
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00408_UAV_outdoor6——————————————
00408_UAV_outdoor6 , fps:6.219267515192817
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00410_UAV_outdoor6——————————————
00410_UAV_outdoor6 , fps:4.210809203505041
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00471_UAV_outdoor6——————————————
00471_UAV_outdoor6 , fps:8.097196557553254
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00473_UAV_outdoor6——————————————
00473_UAV_outdoor6 , fps:9.734130393095015
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00478_UAV_outdoor6——————————————
00478_UAV_outdoor6 , fps:5.075003353223882
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00490_UAV_outdoor6——————————————
00490_UAV_outdoor6 , fps:4.772246939340818
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00413_UAV_outdoor6——————————————
00413_UAV_outdoor6 , fps:9.569300812708418
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00416_UAV_outdoor6——————————————
00416_UAV_outdoor6 , fps:7.032466982271435
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_15_15_36_redcar——————————————
dvSave-2021_02_06_15_15_36_redcar , fps:7.546168218973794
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_15_17_48_whitecar——————————————
dvSave-2021_02_06_15_17_48_whitecar , fps:6.240560283256238
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00445_UAV_outdoor6——————————————
00445_UAV_outdoor6 , fps:6.461848372509411
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00447_UAV_outdoor6——————————————
00447_UAV_outdoor6 , fps:7.401032636173345
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_08_56_18_windowPattern——————————————
dvSave-2021_02_06_08_56_18_windowPattern , fps:6.337926608351434
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_08_56_40_windowPattern2——————————————
dvSave-2021_02_06_08_56_40_windowPattern2 , fps:6.500898392755234
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00241_tennis_outdoor4——————————————
00241_tennis_outdoor4 , fps:7.18857439621594
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00340_UAV_outdoor6——————————————
00340_UAV_outdoor6 , fps:8.542992647222194
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_09_36_15_Pedestrian——————————————
dvSave-2021_02_06_09_36_15_Pedestrian , fps:6.1818347502500695
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_09_36_44_Pedestrian——————————————
dvSave-2021_02_06_09_36_44_Pedestrian , fps:4.491652624384995
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00432_UAV_outdoor6——————————————
00432_UAV_outdoor6 , fps:7.0820524883093015
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00433_UAV_outdoor6——————————————
00433_UAV_outdoor6 , fps:6.4828392226629274
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_04_20_41_53——————————————
dvSave-2021_02_04_20_41_53 , fps:8.310422094282872
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_04_20_49_43——————————————
dvSave-2021_02_04_20_49_43 , fps:7.303337800901853
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00464_UAV_outdoor6——————————————
00464_UAV_outdoor6 , fps:2.358192686648483
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00466_UAV_outdoor6——————————————
00466_UAV_outdoor6 , fps:2.14667426169713
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_17_36_49_personBasketball——————————————
dvSave-2021_02_06_17_36_49_personBasketball , fps:4.260719749508894
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_17_41_45_personBasketball——————————————
dvSave-2021_02_06_17_41_45_personBasketball , fps:7.876213152263312
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_09_16_35_car——————————————
dvSave-2021_02_06_09_16_35_car , fps:8.019698265861392
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_09_21_53_car——————————————
dvSave-2021_02_06_09_21_53_car , fps:6.678804658971108
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_09_24_26_Pedestrian1——————————————
dvSave-2021_02_06_09_24_26_Pedestrian1 , fps:6.471856763643625
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_09_35_08_Pedestrian——————————————
dvSave-2021_02_06_09_35_08_Pedestrian , fps:3.364070798522627
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_15_18_36_redcar——————————————
dvSave-2021_02_06_15_18_36_redcar , fps:6.450804329211388
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_17_15_20_whitecar——————————————
dvSave-2021_02_06_17_15_20_whitecar , fps:6.705081812971683
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_17_16_26_whitecar——————————————
dvSave-2021_02_06_17_16_26_whitecar , fps:6.093635355575591
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_17_20_28_personFootball——————————————
dvSave-2021_02_06_17_20_28_personFootball , fps:2.3193972580340163
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00385_UAV_outdoor6——————————————
00385_UAV_outdoor6 , fps:6.44234109929246
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00398_UAV_outdoor6——————————————
00398_UAV_outdoor6 , fps:8.613842462023781
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_10_11_59_paperClips——————————————
dvSave-2021_02_06_10_11_59_paperClips , fps:5.980383080713924
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_10_14_17_paperClip——————————————
dvSave-2021_02_06_10_14_17_paperClip , fps:3.4821821241794133
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00503_UAV_outdoor6——————————————
00503_UAV_outdoor6 , fps:2.7249453546913016
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: 00506_person_outdoor6——————————————
00506_person_outdoor6 , fps:1.6710433467475567
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_17_21_41_personFootball——————————————
dvSave-2021_02_06_17_21_41_personFootball , fps:2.0125063850067106
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_17_23_26_personFootball——————————————
dvSave-2021_02_06_17_23_26_personFootball , fps:3.125230894912879
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: UAV_long_001——————————————
UAV_long_001 , fps:3.4072992846170047
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dightNUM_001——————————————
dightNUM_001 , fps:7.657855821343084
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_14_16_56_01_house——————————————
dvSave-2021_02_14_16_56_01_house , fps:7.541222371434487
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_14_17_00_48——————————————
dvSave-2021_02_14_17_00_48 , fps:7.526268692678561
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_17_45_17_personBasketball——————————————
dvSave-2021_02_06_17_45_17_personBasketball , fps:6.787196114861898
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_17_47_49_personBasketball——————————————
dvSave-2021_02_06_17_47_49_personBasketball , fps:7.534457980765328
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_17_49_51_personBasketball——————————————
dvSave-2021_02_06_17_49_51_personBasketball , fps:6.863155538334636
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_17_51_05_personBasketball——————————————
dvSave-2021_02_06_17_51_05_personBasketball , fps:7.434436025576185
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_04_20_56_55——————————————
dvSave-2021_02_04_20_56_55 , fps:2.8086951712896204
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_04_21_18_52——————————————
dvSave-2021_02_04_21_18_52 , fps:1.7580584809265254
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_04_21_20_22——————————————
dvSave-2021_02_04_21_20_22 , fps:2.132084876926354
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_04_21_21_24——————————————
dvSave-2021_02_04_21_21_24 , fps:1.9186971557733152
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_10_12_19_basketball——————————————
dvSave-2021_02_15_10_12_19_basketball , fps:7.396881326516497
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_10_14_18_chicken——————————————
dvSave-2021_02_15_10_14_18_chicken , fps:7.402011536866064
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_10_22_23_basketball——————————————
dvSave-2021_02_15_10_22_23_basketball , fps:7.627230819174167
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_10_22_23_boyhead——————————————
dvSave-2021_02_15_10_22_23_boyhead , fps:7.434251656662063
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_17_53_39_personFootball——————————————
dvSave-2021_02_06_17_53_39_personFootball , fps:3.356423592704979
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_17_57_54_personFootball——————————————
dvSave-2021_02_06_17_57_54_personFootball , fps:3.330683410837936
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_08_21_05_56_motor——————————————
dvSave-2021_02_08_21_05_56_motor , fps:2.6049979621783765
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_08_21_07_52——————————————
dvSave-2021_02_08_21_07_52 , fps:2.2255533650210464
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_10_23_05_basketall——————————————
dvSave-2021_02_15_10_23_05_basketall , fps:7.313827511789666
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_10_23_05_boyhead——————————————
dvSave-2021_02_15_10_23_05_boyhead , fps:7.05354023243788
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_10_26_11_chicken——————————————
dvSave-2021_02_15_10_26_11_chicken , fps:7.359768938152808
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_12_44_27_chicken——————————————
dvSave-2021_02_15_12_44_27_chicken , fps:6.560432547477133
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_13_10_54_person——————————————
dvSave-2021_02_15_13_10_54_person , fps:5.744026916509378
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_13_12_45_redcar——————————————
dvSave-2021_02_15_13_12_45_redcar , fps:5.9104961015308275
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_13_13_44_whitecar——————————————
dvSave-2021_02_15_13_13_44_whitecar , fps:5.790387868818712
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_13_14_18_blackcar——————————————
dvSave-2021_02_15_13_14_18_blackcar , fps:5.827336370970386
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_17_27_53_personFootball——————————————
dvSave-2021_02_06_17_27_53_personFootball , fps:2.9902388228146783
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_17_31_03_personBasketball——————————————
dvSave-2021_02_06_17_31_03_personBasketball , fps:2.6279515562949443
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_17_33_01_personBasketball——————————————
dvSave-2021_02_06_17_33_01_personBasketball , fps:2.936614462333512
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_17_34_58_personBasketball——————————————
dvSave-2021_02_06_17_34_58_personBasketball , fps:3.112791533206165
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_13_24_03_girlhead——————————————
dvSave-2021_02_15_13_24_03_girlhead , fps:5.2584813491413165
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_13_24_49_girlhead——————————————
dvSave-2021_02_15_13_24_49_girlhead , fps:5.326520450113515
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_13_27_20_bottle——————————————
dvSave-2021_02_15_13_27_20_bottle , fps:5.319555838978684
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_13_28_20_cash——————————————
dvSave-2021_02_15_13_28_20_cash , fps:5.425459833166672
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_13_01_16_Duck——————————————
dvSave-2021_02_15_13_01_16_Duck , fps:2.0975619763742737
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_13_02_21_Chicken——————————————
dvSave-2021_02_15_13_02_21_Chicken , fps:2.2427673746718306
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_13_04_57_Duck——————————————
dvSave-2021_02_15_13_04_57_Duck , fps:1.9024905913127221
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_13_05_43_Chicken——————————————
dvSave-2021_02_15_13_05_43_Chicken , fps:2.0197297105243077
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_12_45_02_Duck——————————————
dvSave-2021_02_15_12_45_02_Duck , fps:6.952888397672133
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_12_53_54_personHead——————————————
dvSave-2021_02_15_12_53_54_personHead , fps:6.639187707207852
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_16_17_12_18——————————————
dvSave-2021_02_16_17_12_18 , fps:6.909624227433927
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_16_17_15_53——————————————
dvSave-2021_02_16_17_15_53 , fps:7.124915314540273
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_10_17_16_paperClips——————————————
dvSave-2021_02_06_10_17_16_paperClips , fps:2.1294289759579574
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_15_08_41_flag——————————————
dvSave-2021_02_06_15_08_41_flag , fps:2.4296279029528107
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_15_12_44_car——————————————
dvSave-2021_02_06_15_12_44_car , fps:2.193449042828282
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_15_14_26_blackcar——————————————
dvSave-2021_02_06_15_14_26_blackcar , fps:2.476317303279741
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_12_13_43_54——————————————
dvSave-2021_02_12_13_43_54 , fps:2.1377028225640418
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_12_13_46_18——————————————
dvSave-2021_02_12_13_46_18 , fps:2.837450462365385
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_12_13_56_29——————————————
dvSave-2021_02_12_13_56_29 , fps:2.2795085572894664
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_14_16_21_40——————————————
dvSave-2021_02_14_16_21_40 , fps:2.756361205722947
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_23_51_36——————————————
dvSave-2021_02_15_23_51_36 , fps:5.187544804461501
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_23_54_17——————————————
dvSave-2021_02_15_23_54_17 , fps:5.916800241784978
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_23_56_17——————————————
dvSave-2021_02_15_23_56_17 , fps:5.908432431098895
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_16_17_07_38——————————————
dvSave-2021_02_16_17_07_38 , fps:6.853005264607831
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_09_58_27_DigitAI——————————————
dvSave-2021_02_06_09_58_27_DigitAI , fps:5.712036292749114
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_10_03_17_GreenPlant——————————————
dvSave-2021_02_06_10_03_17_GreenPlant , fps:2.239850448528546
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_10_05_38_phone——————————————
dvSave-2021_02_06_10_05_38_phone , fps:2.5375509871976507
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_06_10_09_04_bottle——————————————
dvSave-2021_02_06_10_09_04_bottle , fps:2.264722087319946
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0005——————————————
video_0005 , fps:5.10878665692594
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0008——————————————
video_0008 , fps:5.373578724643246
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0015——————————————
video_0015 , fps:5.149187934265183
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0018——————————————
video_0018 , fps:5.594355155543431
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_16_17_38_25——————————————
dvSave-2021_02_16_17_38_25 , fps:2.43468366240493
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_16_17_42_50——————————————
dvSave-2021_02_16_17_42_50 , fps:2.2103049985325582
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dydrant_001——————————————
dydrant_001 , fps:2.3506965386220364
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: roadLight_001——————————————
roadLight_001 , fps:3.488508786624002
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_12_13_38_26——————————————
dvSave-2021_02_12_13_38_26 , fps:3.2271960264662387
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_12_13_39_56——————————————
dvSave-2021_02_12_13_39_56 , fps:2.4958643694104365
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0070——————————————
video_0070 , fps:3.181040396154057
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0073——————————————
video_0073 , fps:2.8169604317231087
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_13_08_12_blackcar——————————————
dvSave-2021_02_15_13_08_12_blackcar , fps:2.1899101364088627
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_15_13_09_09_person——————————————
dvSave-2021_02_15_13_09_09_person , fps:2.3689763382375384
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0054——————————————
video_0054 , fps:2.2353631043239077
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0056——————————————
video_0056 , fps:3.7382444123169023
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: tennis_long_003——————————————
tennis_long_003 , fps:2.7381794145852334
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: tennis_long_004——————————————
tennis_long_004 , fps:2.796477866529081
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: tennis_long_005——————————————
tennis_long_005 , fps:3.526431788063106
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: tennis_long_006——————————————
tennis_long_006 , fps:4.477892169100046
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0029——————————————
video_0029 , fps:2.654885600487992
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0032——————————————
video_0032 , fps:3.6231226004255896
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0039——————————————
video_0039 , fps:4.722385607689114
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0045——————————————
video_0045 , fps:5.187545438880969
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_14_16_22_06——————————————
dvSave-2021_02_14_16_22_06 , fps:2.983246460878792
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_14_16_53_15_flag——————————————
dvSave-2021_02_14_16_53_15_flag , fps:2.6452639430319733
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_16_17_20_20——————————————
dvSave-2021_02_16_17_20_20 , fps:8.44508983280719
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_16_17_23_10——————————————
dvSave-2021_02_16_17_23_10 , fps:7.799393168144743
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_16_17_29_37——————————————
dvSave-2021_02_16_17_29_37 , fps:7.1556991840940105
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: dvSave-2021_02_16_17_34_11——————————————
dvSave-2021_02_16_17_34_11 , fps:6.00536586517035
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0076——————————————
video_0076 , fps:2.504251790977317
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0079——————————————
video_0079 , fps:4.167390563649277
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: tennis_long_001——————————————
tennis_long_001 , fps:4.677458445621137
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: tennis_long_002——————————————
tennis_long_002 , fps:4.1899166581098015
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0021——————————————
video_0021 , fps:7.491735455266898
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0026——————————————
video_0026 , fps:6.52506040190916
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: tennis_long_007——————————————
tennis_long_007 , fps:5.988987694409126
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0004——————————————
video_0004 , fps:5.976633432917147
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0058——————————————
video_0058 , fps:4.249994419654756
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0060——————————————
video_0060 , fps:4.800748261661136
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0064——————————————
video_0064 , fps:5.672977403693982
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'prompt', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['FE108'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['FE108_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 77}}
——————————Process sequence: video_0067——————————————
video_0067 , fps:8.827698032723104
Totally cost 1h 30m 12s!
