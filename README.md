

### Training
Dowmload the pretrained [foundation model](https://drive.google.com/drive/folders/1ttafo0O5S9DXK2PX0YqPvPrQ-HWJjhSy?usp=sharing) (OSTrack) 
and put it under ./pretrained/.
```
bash train_vipt.sh
```
You can train models with various modalities and variants by modifying ```train_vipt.sh```.

### Testing

```
nohup python ./RGBE_workspace/test_rgbe_mgpus.py --script_name vipt --yaml_name coesot4 --dataset COESOT --threads 4 --epoch 60 > test4.log 2>&1 &
```

#### For loss plotting
```
python scripts/eval/loss.py logs/train_visevent.log --field val --output_name val_2 --output_dir logs/loss/visevent
```


