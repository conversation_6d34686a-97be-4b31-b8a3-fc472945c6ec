import numpy as np
import scipy.stats as stats
from scipy import stats
import pandas as pd

class StatisticalSignificanceTest:
    """统计显著性检验工具"""
    
    def __init__(self):
        self.results = {}
        
    def paired_t_test(self, baseline_scores, our_scores, method_name="Our Method"):
        """
        配对t检验
        Args:
            baseline_scores: 基线方法的得分列表
            our_scores: 我们方法的得分列表  
            method_name: 方法名称
        """
        # 配对t检验
        t_stat, p_value = stats.ttest_rel(our_scores, baseline_scores)
        
        # 效应大小 (<PERSON>'s d)
        diff = np.array(our_scores) - np.array(baseline_scores)
        pooled_std = np.sqrt((np.var(our_scores) + np.var(baseline_scores)) / 2)
        cohens_d = np.mean(diff) / pooled_std
        
        # 置信区间
        conf_interval = stats.t.interval(0.95, len(diff)-1, 
                                       loc=np.mean(diff), 
                                       scale=stats.sem(diff))
        
        result = {
            'method': method_name,
            'mean_improvement': np.mean(diff),
            'std_improvement': np.std(diff),
            't_statistic': t_stat,
            'p_value': p_value,
            'cohens_d': cohens_d,
            'confidence_interval': conf_interval,
            'is_significant': p_value < 0.05,
            'effect_size': self._interpret_cohens_d(cohens_d)
        }
        
        self.results[method_name] = result
        return result
    
    def _interpret_cohens_d(self, d):
        """解释Cohen's d效应大小"""
        abs_d = abs(d)
        if abs_d < 0.2:
            return "Small"
        elif abs_d < 0.5:
            return "Medium"  
        elif abs_d < 0.8:
            return "Large"
        else:
            return "Very Large"
    
    def wilcoxon_test(self, baseline_scores, our_scores, method_name="Our Method"):
        """
        Wilcoxon符号秩检验 (非参数检验)
        适用于数据不满足正态分布的情况
        """
        statistic, p_value = stats.wilcoxon(our_scores, baseline_scores)
        
        result = {
            'method': method_name,
            'wilcoxon_statistic': statistic,
            'p_value': p_value,
            'is_significant': p_value < 0.05
        }
        
        return result
    
    def bootstrap_confidence_interval(self, baseline_scores, our_scores, 
                                    n_bootstrap=1000, confidence=0.95):
        """
        Bootstrap置信区间估计
        """
        def bootstrap_mean_diff(baseline, ours, n_samples):
            n = len(baseline)
            bootstrap_diffs = []
            
            for _ in range(n_samples):
                # 有放回抽样
                indices = np.random.choice(n, n, replace=True)
                baseline_sample = [baseline[i] for i in indices]
                ours_sample = [ours[i] for i in indices]
                
                diff = np.mean(ours_sample) - np.mean(baseline_sample)
                bootstrap_diffs.append(diff)
                
            return np.array(bootstrap_diffs)
        
        bootstrap_diffs = bootstrap_mean_diff(baseline_scores, our_scores, n_bootstrap)
        
        alpha = 1 - confidence
        lower_percentile = (alpha/2) * 100
        upper_percentile = (1 - alpha/2) * 100
        
        ci_lower = np.percentile(bootstrap_diffs, lower_percentile)
        ci_upper = np.percentile(bootstrap_diffs, upper_percentile)
        
        return {
            'bootstrap_mean': np.mean(bootstrap_diffs),
            'bootstrap_std': np.std(bootstrap_diffs),
            'confidence_interval': (ci_lower, ci_upper),
            'confidence_level': confidence
        }
    
    def generate_significance_table(self):
        """生成统计显著性表格"""
        if not self.results:
            return "No results available"
            
        df_data = []
        for method, result in self.results.items():
            df_data.append({
                'Method': method,
                'Mean Improvement': f"{result['mean_improvement']:.3f}",
                'Std': f"{result['std_improvement']:.3f}",
                't-statistic': f"{result['t_statistic']:.3f}",
                'p-value': f"{result['p_value']:.4f}",
                "Cohen's d": f"{result['cohens_d']:.3f}",
                'Effect Size': result['effect_size'],
                'Significant': '✓' if result['is_significant'] else '✗',
                '95% CI': f"[{result['confidence_interval'][0]:.3f}, {result['confidence_interval'][1]:.3f}]"
            })
        
        return pd.DataFrame(df_data)

# 使用示例
def run_significance_tests():
    """运行统计显著性检验的示例"""
    
    # 模拟实验数据 (实际使用时替换为真实数据)
    # 假设在30个视频序列上进行了测试
    np.random.seed(42)
    
    # 基线方法得分 (模拟数据)
    baseline_scores = np.random.normal(65.0, 3.0, 30)  # 均值65%，标准差3%
    
    # 我们的方法得分 (模拟数据，假设有2%的提升)
    our_scores = baseline_scores + np.random.normal(2.0, 1.0, 30)  # 平均提升2%
    
    # 创建检验工具
    tester = StatisticalSignificanceTest()
    
    # 进行配对t检验
    result = tester.paired_t_test(baseline_scores, our_scores, "DualPrompt")
    
    print("=== 统计显著性检验结果 ===")
    print(f"平均改进: {result['mean_improvement']:.3f}%")
    print(f"标准差: {result['std_improvement']:.3f}%") 
    print(f"t统计量: {result['t_statistic']:.3f}")
    print(f"p值: {result['p_value']:.4f}")
    print(f"Cohen's d: {result['cohens_d']:.3f} ({result['effect_size']})")
    print(f"95%置信区间: [{result['confidence_interval'][0]:.3f}, {result['confidence_interval'][1]:.3f}]")
    print(f"统计显著: {'是' if result['is_significant'] else '否'}")
    
    # Bootstrap置信区间
    bootstrap_result = tester.bootstrap_confidence_interval(baseline_scores, our_scores)
    print(f"\nBootstrap 95%置信区间: [{bootstrap_result['confidence_interval'][0]:.3f}, {bootstrap_result['confidence_interval'][1]:.3f}]")
    
    # 生成表格
    table = tester.generate_significance_table()
    print("\n=== 统计显著性表格 ===")
    print(table.to_string(index=False))
    
    return tester

if __name__ == "__main__":
    tester = run_significance_tests()
