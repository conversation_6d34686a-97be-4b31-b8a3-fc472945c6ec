#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练和验证Loss信息提取工具
从visevent.log中提取每个epoch的平均训练和验证损失，保存为格式化的CSV和TXT文件
"""

import re
import csv
import argparse
from collections import defaultdict
from pathlib import Path


class LossSummaryExtractor:
    def __init__(self, log_file: str):
        self.log_file = Path(log_file)
        
        # 正则表达式模式
        self.train_pattern = re.compile(r'\[train: (\d+), \d+ / \d+\].*?Loss/total: ([\d\.]+).*?Loss/giou: ([\d\.]+).*?Loss/l1: ([\d\.]+).*?Loss/location: ([\d\.]+).*?IoU: ([\d\.]+)')
        self.val_pattern = re.compile(r'\[val: (\d+), \d+ / \d+\].*?Loss/total: ([\d\.]+).*?Loss/giou: ([\d\.]+).*?Loss/l1: ([\d\.]+).*?Loss/location: ([\d\.]+).*?IoU: ([\d\.]+)')
        self.epoch_time_pattern = re.compile(r'Epoch Time: (\d+:\d+:\d+\.\d+)')
        
    def parse_log(self):
        """解析日志文件，提取训练和验证数据"""
        if not self.log_file.exists():
            raise FileNotFoundError(f"日志文件不存在: {self.log_file}")
        
        train_data = defaultdict(lambda: defaultdict(list))
        val_data = defaultdict(lambda: defaultdict(list))
        epoch_times = {}
        
        with open(self.log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        current_epoch = None
        epoch_time_count = 0
        
        for line in lines:
            line = line.strip()
            
            # 解析训练数据
            train_match = self.train_pattern.search(line)
            if train_match:
                epoch = int(train_match.group(1))
                current_epoch = epoch
                
                train_data[epoch]['total'].append(float(train_match.group(2)))
                train_data[epoch]['giou'].append(float(train_match.group(3)))
                train_data[epoch]['l1'].append(float(train_match.group(4)))
                train_data[epoch]['location'].append(float(train_match.group(5)))
                train_data[epoch]['iou'].append(float(train_match.group(6)))
            
            # 解析验证数据
            val_match = self.val_pattern.search(line)
            if val_match:
                epoch = int(val_match.group(1))
                
                val_data[epoch]['total'].append(float(val_match.group(2)))
                val_data[epoch]['giou'].append(float(val_match.group(3)))
                val_data[epoch]['l1'].append(float(val_match.group(4)))
                val_data[epoch]['location'].append(float(val_match.group(5)))
                val_data[epoch]['iou'].append(float(val_match.group(6)))
            
            # 解析epoch时间
            time_match = self.epoch_time_pattern.search(line)
            if time_match and current_epoch is not None:
                epoch_time_count += 1
                # 每个epoch有多个时间记录，我们取最后一个作为该epoch的总时间
                epoch_times[current_epoch] = time_match.group(1)
        
        return self._calculate_averages(train_data, val_data, epoch_times)
    
    def _calculate_averages(self, train_data, val_data, epoch_times):
        """计算每个epoch的平均值"""
        summary = {}
        
        all_epochs = set(train_data.keys()) | set(val_data.keys())
        
        for epoch in sorted(all_epochs):
            summary[epoch] = {
                'epoch': epoch,
                'train': {},
                'val': {},
                'epoch_time': epoch_times.get(epoch, 'N/A')
            }
            
            # 计算训练数据平均值
            if epoch in train_data:
                for metric, values in train_data[epoch].items():
                    if values:
                        summary[epoch]['train'][metric] = sum(values) / len(values)
                    else:
                        summary[epoch]['train'][metric] = 0.0
            
            # 计算验证数据平均值
            if epoch in val_data:
                for metric, values in val_data[epoch].items():
                    if values:
                        summary[epoch]['val'][metric] = sum(values) / len(values)
                    else:
                        summary[epoch]['val'][metric] = 0.0
        
        return summary
    
    def save_to_csv(self, summary, output_file: str):
        """保存数据到CSV文件"""
        output_path = Path(output_file)
        
        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入表头
            header = [
                'epoch', 'epoch_time',
                'train_loss_total', 'train_loss_giou', 'train_loss_l1', 'train_loss_location', 'train_iou',
                'val_loss_total', 'val_loss_giou', 'val_loss_l1', 'val_loss_location', 'val_iou'
            ]
            writer.writerow(header)
            
            # 写入数据
            for epoch in sorted(summary.keys()):
                data = summary[epoch]
                row = [
                    epoch,
                    data['epoch_time'],
                    data['train'].get('total', ''),
                    data['train'].get('giou', ''),
                    data['train'].get('l1', ''),
                    data['train'].get('location', ''),
                    data['train'].get('iou', ''),
                    data['val'].get('total', ''),
                    data['val'].get('giou', ''),
                    data['val'].get('l1', ''),
                    data['val'].get('location', ''),
                    data['val'].get('iou', '')
                ]
                writer.writerow(row)
        
        print(f"CSV数据已保存到: {output_path}")
    
    def save_to_txt(self, summary, output_file: str):
        """保存数据到格式化的TXT文件"""
        output_path = Path(output_file)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("训练和验证Loss摘要报告\n")
            f.write("=" * 80 + "\n\n")
            
            # 写入总体统计
            total_epochs = len(summary)
            train_epochs = len([e for e in summary.values() if e['train']])
            val_epochs = len([e for e in summary.values() if e['val']])
            
            f.write(f"总Epoch数: {total_epochs}\n")
            f.write(f"有训练数据的Epoch数: {train_epochs}\n")
            f.write(f"有验证数据的Epoch数: {val_epochs}\n\n")
            
            # 写入详细数据
            for epoch in sorted(summary.keys()):
                data = summary[epoch]
                f.write(f"Epoch {epoch}\n")
                f.write("-" * 60 + "\n")
                f.write(f"训练时间: {data['epoch_time']}\n\n")
                
                # 训练数据
                if data['train']:
                    f.write("训练指标:\n")
                    f.write(f"  Total Loss:    {data['train'].get('total', 'N/A'):.6f}\n")
                    f.write(f"  GIoU Loss:     {data['train'].get('giou', 'N/A'):.6f}\n")
                    f.write(f"  L1 Loss:       {data['train'].get('l1', 'N/A'):.6f}\n")
                    f.write(f"  Location Loss: {data['train'].get('location', 'N/A'):.6f}\n")
                    f.write(f"  IoU:           {data['train'].get('iou', 'N/A'):.6f}\n")
                else:
                    f.write("训练指标: 无数据\n")
                
                f.write("\n")
                
                # 验证数据
                if data['val']:
                    f.write("验证指标:\n")
                    f.write(f"  Total Loss:    {data['val'].get('total', 'N/A'):.6f}\n")
                    f.write(f"  GIoU Loss:     {data['val'].get('giou', 'N/A'):.6f}\n")
                    f.write(f"  L1 Loss:       {data['val'].get('l1', 'N/A'):.6f}\n")
                    f.write(f"  Location Loss: {data['val'].get('location', 'N/A'):.6f}\n")
                    f.write(f"  IoU:           {data['val'].get('iou', 'N/A'):.6f}\n")
                else:
                    f.write("验证指标: 无数据\n")
                
                f.write("\n" + "=" * 80 + "\n\n")
            
            # 写入趋势分析
            f.write("趋势分析\n")
            f.write("=" * 80 + "\n")
            
            # 训练loss趋势
            train_losses = [(e, d['train'].get('total', 0)) for e, d in summary.items() if d['train']]
            if len(train_losses) >= 2:
                first_loss = train_losses[0][1]
                last_loss = train_losses[-1][1]
                improvement = first_loss - last_loss
                f.write(f"训练Loss变化: {first_loss:.6f} -> {last_loss:.6f} (改善: {improvement:.6f})\n")
            
            # 验证loss趋势
            val_losses = [(e, d['val'].get('total', 0)) for e, d in summary.items() if d['val']]
            if len(val_losses) >= 2:
                first_loss = val_losses[0][1]
                last_loss = val_losses[-1][1]
                improvement = first_loss - last_loss
                f.write(f"验证Loss变化: {first_loss:.6f} -> {last_loss:.6f} (改善: {improvement:.6f})\n")
            
            # IoU趋势
            train_ious = [(e, d['train'].get('iou', 0)) for e, d in summary.items() if d['train']]
            if len(train_ious) >= 2:
                first_iou = train_ious[0][1]
                last_iou = train_ious[-1][1]
                improvement = last_iou - first_iou
                f.write(f"训练IoU变化: {first_iou:.6f} -> {last_iou:.6f} (改善: {improvement:.6f})\n")
            
            val_ious = [(e, d['val'].get('iou', 0)) for e, d in summary.items() if d['val']]
            if len(val_ious) >= 2:
                first_iou = val_ious[0][1]
                last_iou = val_ious[-1][1]
                improvement = last_iou - first_iou
                f.write(f"验证IoU变化: {first_iou:.6f} -> {last_iou:.6f} (改善: {improvement:.6f})\n")
        
        print(f"格式化报告已保存到: {output_path}")
    
    def print_summary(self, summary):
        """打印摘要信息"""
        print(f"\n=== Loss摘要信息 ===")
        print(f"总共解析了 {len(summary)} 个epoch的数据")
        
        train_count = len([e for e in summary.values() if e['train']])
        val_count = len([e for e in summary.values() if e['val']])
        
        print(f"有训练数据的epoch: {train_count}")
        print(f"有验证数据的epoch: {val_count}")
        
        # 显示前3个epoch的示例
        print(f"\n=== 前3个epoch的示例数据 ===")
        for epoch in sorted(list(summary.keys())[:3]):
            data = summary[epoch]
            print(f"\nEpoch {epoch}:")
            if data['train']:
                print(f"  训练 - Total Loss: {data['train'].get('total', 0):.5f}, IoU: {data['train'].get('iou', 0):.5f}")
            if data['val']:
                print(f"  验证 - Total Loss: {data['val'].get('total', 0):.5f}, IoU: {data['val'].get('iou', 0):.5f}")


def main():
    parser = argparse.ArgumentParser(description='提取训练和验证Loss摘要')
    parser.add_argument('log_file', help='输入的日志文件路径')
    parser.add_argument('--output', '-o', default='loss_summary', 
                       help='输出文件前缀 (默认: loss_summary)')
    parser.add_argument('--format', '-f', choices=['csv', 'txt', 'both'], 
                       default='both', help='输出格式 (默认: both)')
    
    args = parser.parse_args()
    
    try:
        # 解析日志
        extractor = LossSummaryExtractor(args.log_file)
        print(f"正在解析日志文件: {args.log_file}")
        summary = extractor.parse_log()
        
        # 打印摘要
        extractor.print_summary(summary)
        
        # 保存数据
        if args.format in ['csv', 'both']:
            extractor.save_to_csv(summary, f"{args.output}.csv")
        
        if args.format in ['txt', 'both']:
            extractor.save_to_txt(summary, f"{args.output}.txt")
        
        print(f"\n解析完成！")
        
    except Exception as e:
        print(f"错误: {e}")
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())
