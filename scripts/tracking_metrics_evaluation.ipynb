{"cells": [{"cell_type": "markdown", "id": "baeb682f", "metadata": {}, "source": ["# 目标跟踪结果指标计算\n", "\n", "本Notebook用于计算目标跟踪算法在FE108数据集上的性能指标，包括：\n", "- **AUC (Area Under Curve)**: 成功率曲线下面积\n", "- **OP50**: Overlap Precision at threshold 0.5\n", "- **OP75**: Overlap Precision at threshold 0.75\n", "- **RSR (Robust Success Rate)**: 鲁棒成功率\n", "- **RPR (Robust Precision Rate)**: 鲁棒精确率\n", "\n", "数据格式：\n", "- 预测结果：`RGBE_workspace/results/FE108/fe108_ep59/序列名.txt` (x, y, w, h)\n", "- 真实标注：`data/FE108/test/序列名/groundtruth_rect.txt` (x, y, w, h)"]}, {"cell_type": "code", "execution_count": null, "id": "da3f13e8", "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置绘图风格\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "plt.rcParams['font.size'] = 12"]}, {"cell_type": "code", "execution_count": null, "id": "9cadc22d", "metadata": {}, "outputs": [], "source": ["# 配置路径\n", "BASE_DIR = Path('/home/<USER>/STU/workspaces/ruihui/ViPT')\n", "RESULTS_BASE_DIR = BASE_DIR / 'RGBE_workspace/results/FE108'\n", "GT_DIR = BASE_DIR / 'data/FE108/test'\n", "\n", "# 获取所有模型文件夹\n", "model_folders = [d for d in RESULTS_BASE_DIR.iterdir() if d.is_dir()]\n", "model_names = [d.name for d in model_folders]\n", "\n", "print(f\"找到 {len(model_names)} 个模型结果:\")\n", "for model_name in sorted(model_names):\n", "    print(f\"  - {model_name}\")\n", "print(f\"\\n真实标注文件夹: {GT_DIR}\")\n", "print(f\"标注文件夹存在: {GT_DIR.exists()}\")"]}, {"cell_type": "code", "execution_count": null, "id": "25f821d3", "metadata": {}, "outputs": [], "source": ["def load_bbox_data(file_path):\n", "    \"\"\"\n", "    加载边界框数据 (x, y, w, h)\n", "    \"\"\"\n", "    try:\n", "        data = np.loadtxt(file_path, delimiter=',')\n", "        if data.ndim == 1:\n", "            data = data.reshape(1, -1)\n", "        return data\n", "    except Exception as e:\n", "        print(f\"加载文件失败: {file_path}, 错误: {e}\")\n", "        return None\n", "\n", "def calculate_iou(box1, box2):\n", "    \"\"\"\n", "    计算两个边界框的IoU\n", "    box: [x, y, w, h]\n", "    \"\"\"\n", "    x1, y1, w1, h1 = box1\n", "    x2, y2, w2, h2 = box2\n", "    \n", "    # 计算交集\n", "    x_left = max(x1, x2)\n", "    y_top = max(y1, y2)\n", "    x_right = min(x1 + w1, x2 + w2)\n", "    y_bottom = min(y1 + h1, y2 + h2)\n", "    \n", "    if x_right < x_left or y_bottom < y_top:\n", "        return 0.0\n", "    \n", "    intersection = (x_right - x_left) * (y_bottom - y_top)\n", "    union = w1 * h1 + w2 * h2 - intersection\n", "    \n", "    return intersection / union if union > 0 else 0.0\n", "\n", "def calculate_center_error(box1, box2):\n", "    \"\"\"\n", "    计算两个边界框中心点的欧氏距离\n", "    \"\"\"\n", "    x1, y1, w1, h1 = box1\n", "    x2, y2, w2, h2 = box2\n", "    \n", "    center1 = np.array([x1 + w1/2, y1 + h1/2])\n", "    center2 = np.array([x2 + w2/2, y2 + h2/2])\n", "    \n", "    return np.linalg.norm(center1 - center2)"]}, {"cell_type": "code", "execution_count": null, "id": "4ae2d9bb", "metadata": {}, "outputs": [], "source": ["def evaluate_sequence(pred_boxes, gt_boxes, seq_name):\n", "    \"\"\"\n", "    评估单个序列的跟踪性能\n", "    \"\"\"\n", "    if pred_boxes is None or gt_boxes is None:\n", "        return None\n", "    \n", "    # 确保长度一致\n", "    min_len = min(len(pred_boxes), len(gt_boxes))\n", "    pred_boxes = pred_boxes[:min_len]\n", "    gt_boxes = gt_boxes[:min_len]\n", "    \n", "    # 计算IoU和中心误差\n", "    ious = []\n", "    center_errors = []\n", "    \n", "    for pred, gt in zip(pred_boxes, gt_boxes):\n", "        iou = calculate_iou(pred, gt)\n", "        center_error = calculate_center_error(pred, gt)\n", "        ious.append(iou)\n", "        center_errors.append(center_error)\n", "    \n", "    ious = np.array(ious)\n", "    center_errors = np.array(center_errors)\n", "    \n", "    # 计算各种指标\n", "    results = {\n", "        'sequence': seq_name,\n", "        'num_frames': len(ious),\n", "        'mean_iou': np.mean(ious),\n", "        'mean_center_error': np.mean(center_errors),\n", "        'op50': np.mean(ious >= 0.5),  # Overlap Precision at 0.5\n", "        'op75': np.mean(ious >= 0.75), # Overlap Precision at 0.75\n", "        'ious': ious,\n", "        'center_errors': center_errors\n", "    }\n", "    \n", "    return results\n", "\n", "def calculate_auc(success_rates, overlap_thresholds):\n", "    \"\"\"\n", "    计算成功率曲线下面积 (AUC)\n", "    \"\"\"\n", "    return np.trapz(success_rates, overlap_thresholds)\n", "\n", "def calculate_robust_metrics(all_ious, all_center_errors, iou_threshold=0.5, center_threshold=20):\n", "    \"\"\"\n", "    计算鲁棒成功率和精确率\n", "    \"\"\"\n", "    rsr = np.mean(all_ious >= iou_threshold)  # Robust Success Rate\n", "    rpr = np.mean(all_center_errors <= center_threshold)  # Robust Precision Rate\n", "    return rsr, rpr"]}, {"cell_type": "code", "execution_count": null, "id": "515743bb", "metadata": {}, "outputs": [], "source": ["# 获取所有序列（基于第一个模型的结果文件）\n", "if model_folders:\n", "    first_model_dir = model_folders[0]\n", "    result_files = list(first_model_dir.glob('*.txt'))\n", "    sequences = []\n", "\n", "    for result_file in result_files:\n", "        seq_name = result_file.stem\n", "        gt_file = GT_DIR / seq_name / 'groundtruth_rect.txt'\n", "        \n", "        # 检查所有模型是否都有这个序列的结果\n", "        all_models_have_seq = True\n", "        for model_folder in model_folders:\n", "            if not (model_folder / f\"{seq_name}.txt\").exists():\n", "                all_models_have_seq = False\n", "                break\n", "        \n", "        if gt_file.exists() and all_models_have_seq:\n", "            sequences.append(seq_name)\n", "        else:\n", "            if not gt_file.exists():\n", "                print(f\"警告: 找不到序列 {seq_name} 的真实标注文件\")\n", "            if not all_models_have_seq:\n", "                print(f\"警告: 不是所有模型都有序列 {seq_name} 的结果\")\n", "\n", "    print(f\"找到 {len(sequences)} 个所有模型都有的有效序列:\")\n", "    for seq in sorted(sequences):\n", "        print(f\"  - {seq}\")\n", "else:\n", "    print(\"未找到任何模型结果文件夹\")\n", "    sequences = []"]}, {"cell_type": "code", "execution_count": null, "id": "08af70de", "metadata": {}, "outputs": [], "source": ["# 评估所有模型的所有序列\n", "all_models_results = {}\n", "all_models_ious = {}\n", "all_models_center_errors = {}\n", "\n", "print(\"开始评估所有模型...\")\n", "\n", "for model_folder in model_folders:\n", "    model_name = model_folder.name\n", "    print(f\"\\n评估模型: {model_name}\")\n", "    print(\"-\" * 40)\n", "    \n", "    sequence_results = []\n", "    all_ious = []\n", "    all_center_errors = []\n", "    \n", "    for seq_name in sequences:\n", "        # 加载预测结果和真实标注\n", "        pred_file = model_folder / f\"{seq_name}.txt\"\n", "        gt_file = GT_DIR / seq_name / 'groundtruth_rect.txt'\n", "        \n", "        pred_boxes = load_bbox_data(pred_file)\n", "        gt_boxes = load_bbox_data(gt_file)\n", "        \n", "        # 评估序列\n", "        result = evaluate_sequence(pred_boxes, gt_boxes, seq_name)\n", "        \n", "        if result is not None:\n", "            sequence_results.append(result)\n", "            all_ious.extend(result['ious'])\n", "            all_center_errors.extend(result['center_errors'])\n", "            print(f\"✓ {seq_name}: OP50={result['op50']:.3f}, OP75={result['op75']:.3f}, 平均IoU={result['mean_iou']:.3f}\")\n", "        else:\n", "            print(f\"✗ {seq_name}: 评估失败\")\n", "    \n", "    # 保存每个模型的结果\n", "    all_models_results[model_name] = sequence_results\n", "    all_models_ious[model_name] = np.array(all_ious)\n", "    all_models_center_errors[model_name] = np.array(all_center_errors)\n", "    \n", "    print(f\"模型 {model_name} 成功评估 {len(sequence_results)} 个序列\")\n", "\n", "print(f\"\\n所有模型评估完成！\")"]}, {"cell_type": "code", "execution_count": null, "id": "247e1980", "metadata": {}, "outputs": [], "source": ["# 计算所有模型的整体指标\n", "overlap_thresholds = np.linspace(0, 1, 101)\n", "all_models_metrics = {}\n", "\n", "print(\"=\" * 80)\n", "print(\"所有模型整体评估结果\")\n", "print(\"=\" * 80)\n", "\n", "for model_name in model_names:\n", "    if model_name in all_models_results:\n", "        sequence_results = all_models_results[model_name]\n", "        all_ious = all_models_ious[model_name]\n", "        all_center_errors = all_models_center_errors[model_name]\n", "        \n", "        # 计算成功率曲线和AUC\n", "        success_rates = []\n", "        for threshold in overlap_thresholds:\n", "            success_rate = np.mean(all_ious >= threshold)\n", "            success_rates.append(success_rate)\n", "        \n", "        success_rates = np.array(success_rates)\n", "        auc_score = calculate_auc(success_rates, overlap_thresholds)\n", "        \n", "        # 计算鲁棒指标\n", "        rsr, rpr = calculate_robust_metrics(all_ious, all_center_errors)\n", "        \n", "        # 计算平均指标\n", "        mean_op50 = np.mean([r['op50'] for r in sequence_results])\n", "        mean_op75 = np.mean([r['op75'] for r in sequence_results])\n", "        mean_iou = np.mean(all_ious)\n", "        mean_center_error = np.mean(all_center_errors)\n", "        \n", "        # 计算精确度曲线AUC\n", "        error_thresholds = np.linspace(0, 50, 51)\n", "        precision_rates = []\n", "        for threshold in error_thresholds:\n", "            precision_rate = np.mean(all_center_errors <= threshold)\n", "            precision_rates.append(precision_rate)\n", "        precision_rates = np.array(precision_rates)\n", "        precision_auc = np.trapz(precision_rates, error_thresholds) / 50\n", "        \n", "        # 保存指标\n", "        all_models_metrics[model_name] = {\n", "            'auc_score': auc_score,\n", "            'mean_op50': mean_op50,\n", "            'mean_op75': mean_op75,\n", "            'rsr': rsr,\n", "            'rpr': rpr,\n", "            'mean_iou': mean_iou,\n", "            'mean_center_error': mean_center_error,\n", "            'precision_auc': precision_auc,\n", "            'success_rates': success_rates,\n", "            'precision_rates': precision_rates,\n", "            'total_frames': len(all_ious),\n", "            'total_sequences': len(sequence_results)\n", "        }\n", "        \n", "        # 打印结果\n", "        print(f\"\\n模型: {model_name}\")\n", "        print(f\"总帧数: {len(all_ious):,}, 评估序列数: {len(sequence_results)}\")\n", "        print(f\"  AUC: {auc_score:.4f}\")\n", "        print(f\"  OP50: {mean_op50:.4f} ({mean_op50*100:.2f}%)\")\n", "        print(f\"  OP75: {mean_op75:.4f} ({mean_op75*100:.2f}%)\")\n", "        print(f\"  RSR: {rsr:.4f} ({rsr*100:.2f}%)\")\n", "        print(f\"  RPR: {rpr:.4f} ({rpr*100:.2f}%)\")\n", "        print(f\"  平均IoU: {mean_iou:.4f}\")\n", "        print(f\"  平均中心误差: {mean_center_error:.2f} 像素\")\n", "\n", "print(\"=\" * 80)"]}, {"cell_type": "code", "execution_count": null, "id": "9f31a3ed", "metadata": {}, "outputs": [], "source": ["# 创建多模型对比表格\n", "comparison_data = []\n", "for model_name in sorted(model_names):\n", "    if model_name in all_models_metrics:\n", "        metrics = all_models_metrics[model_name]\n", "        comparison_data.append({\n", "            'Model': model_name,\n", "            'Total_Frames': metrics['total_frames'],\n", "            'Total_Sequences': metrics['total_sequences'],\n", "            'AUC': metrics['auc_score'],\n", "            'OP50': metrics['mean_op50'],\n", "            'OP75': metrics['mean_op75'],\n", "            'RSR': metrics['rsr'],\n", "            'RPR': metrics['rpr'],\n", "            'Mean_IoU': metrics['mean_iou'],\n", "            'Mean_Center_Error': metrics['mean_center_error'],\n", "            'Precision_AUC': metrics['precision_auc']\n", "        })\n", "\n", "df_comparison = pd.DataFrame(comparison_data)\n", "\n", "# 格式化显示\n", "pd.set_option('display.max_rows', None)\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.float_format', '{:.4f}'.format)\n", "\n", "print(\"多模型对比结果:\")\n", "print(\"=\" * 120)\n", "print(df_comparison.to_string(index=False))\n", "print(\"=\" * 120)\n", "\n", "# 找出最佳性能\n", "best_auc = df_comparison.loc[df_comparison['AUC'].idxmax()]\n", "best_op50 = df_comparison.loc[df_comparison['OP50'].idxmax()]\n", "best_op75 = df_comparison.loc[df_comparison['OP75'].idxmax()]\n", "\n", "print(f\"\\n🏆 最佳性能:\")\n", "print(f\"  最高AUC: {best_auc['Model']} ({best_auc['AUC']:.4f})\")\n", "print(f\"  最高OP50: {best_op50['Model']} ({best_op50['OP50']:.4f})\")\n", "print(f\"  最高OP75: {best_op75['Model']} ({best_op75['OP75']:.4f})\")\n", "\n", "# 创建每个序列的详细对比\n", "print(f\"\\n详细序列对比 (仅显示前10个序列):\")\n", "detailed_comparison = []\n", "\n", "for seq_name in sorted(sequences)[:10]:  # 只显示前10个序列\n", "    seq_data = {'Sequence': seq_name}\n", "    for model_name in sorted(model_names):\n", "        if model_name in all_models_results:\n", "            sequence_results = all_models_results[model_name]\n", "            seq_result = next((r for r in sequence_results if r['sequence'] == seq_name), None)\n", "            if seq_result:\n", "                seq_data[f'{model_name}_OP50'] = seq_result['op50']\n", "                seq_data[f'{model_name}_OP75'] = seq_result['op75']\n", "                seq_data[f'{model_name}_IoU'] = seq_result['mean_iou']\n", "    detailed_comparison.append(seq_data)\n", "\n", "df_detailed = pd.DataFrame(detailed_comparison)\n", "print(df_detailed.to_string(index=False))"]}, {"cell_type": "code", "execution_count": 10, "id": "235e7ff2", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABikAAAI5CAYAAADUnNkHAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjguNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8fJSN1AAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOzdd3hT1RvA8W+SpmnTvaCstowyygaZAmWKCKigAioIiAtQUERUREBARJHhT1FRUBAEERUHCih7K1BAhuw9SifdK8n9/REaepN0QaFF38/znKe555577rlJmuTe955zNIqiKAghhBBCCCGEEEIIIYQQQtxm2tJugBBCCCGEEEIIIYQQQggh/pskSCGEEEIIIYQQQgghhBBCiFIhQQohhBBCCCGEEEIIIYQQQpQKCVIIIYQQQgghhBBCCCGEEKJUSJBCCCGEEEIIIYQQQgghhBClQoIUQgghhBBCCCGEEEIIIYQoFRKkEEIIIYQQQgghhBBCCCFEqZAghRBCCCGEEEIIIYQQQgghSoUEKYQQQgghhBBCCCGEEEIIUSokSCGEEEIIcZuFhYWh0WhsaeLEiaXdpGLbuHGj6hg0Gg1nzpwp7WaJYhg0aJDq9Wvfvn1pN0mI22bixImq939YWFhpN0kIIYQQ4j/LpbQbIIQQQog7x6pVq/jll1/Ytm0bly9f5urVqxiNRgIDA2natCkdO3akf//+eHh4lHZTb6uNGzeyceNG27Kvry8vvvjiTdU5e/Zsrl69altu3779Lb+IXBr7FGXbjz/+yL59+2zLYWFhDBo0qNTacyeZOHEib731VrG22bt3L40aNSrROgD27dvHzp072bVrF7t27eLw4cOYzWbb+tDQ0CIHGePi4pgzZw6//fYbJ0+eJCUlhYCAABo0aECvXr148skn0ev1BdaxatUqli1bxp49ezh37hxpaWm4uroSGBhIREQE3bp1Y/DgwXh7exfr2MuiW/H9UNYtWLBA9X5q1KgRDz74YKm1RwghhBBlnwQphBBCCFGoqKgoBg8ezN9//+2wLikpiaSkJE6ePMm3337L2LFjefvtt3nuuedKoaWlY+PGjaoLiaGhoSUSpDh79qwq73YEKW73PkXZ9uOPP7Jw4ULbcmRkpAQp7kAPPvigw//2jfjpp58YNGiQKpgJcPnyZS5fvsyaNWuYNWsWv/zyC+Hh4Q7bJycn88gjj/D77787rMvIyOD8+fOcP3+eNWvW8Pbbb/PDDz/Qpk2bm253aboV3w9l3YIFC9i0aZNteeDAgRKkEEIIIUSBJEghhBBCiAKtWLGCRx99lKysrCKVT0hIYOjQoezdu5e5c+fe4tbdmbZu3YrJZLIt+/r6ll5jblDLli05ffq0Kq9y5cql1BohxK22du1aHn74YdVnlzNHjx6lXbt27N27l+DgYNW6l156yWmAwpnY2Fh69erF8ePH78jPSCGEEEIIUXQSpBBCCCFEvvbt20f//v0dAhR9+vRhyJAhhIWFER8fz4oVK/jggw/Izs62lfnss8+oU6fOv/6O0Rvxb7iY7+bmJmO4C1FMS5cupWXLlvmur1ix4i2pw9PTk7vvvptmzZrRrFkzlixZwq+//lq0RmPt5TBkyBBVgCIkJISPPvqI8PBw1q1bx8svv2z7roiOjmbkyJEsW7bMVj47O5uvv/5aVa+Pjw+zZs2iZcuWxMTEMH78eDZv3mxbHxcXx8qVK+nfv3+R2yqEEEIIIe5AihBCCCFEPjp06KAAqjRlyhSnZdeuXavodDpVWS8vLyU+Pl5VLjIyUlVm4MCBDnV9+eWXDvu1d/DgQWX69OnKY489pjRu3FipUqWK4uHhoej1eiUwMFBp0aKF8vLLLyuHDx/O9/jya8uyZcuUjh07Kn5+forBYFDq1KmjTJo0ScnIyFBtP3DgQId25pe+/PJL23ahoaGqdRMmTLCtK2p9eZ+TnJwcZfHixcqoUaOUjh07KjVr1lQCAwMVFxcXxdPTU6latary4IMPKgsXLlSysrIcnocb2eeGDRsc1p0+fVpRFEXZt2+fw7q///7b6WvwxRdfOLxnUlNTHcqtWrVK6d+/v1KjRg3F09NTMRgMSuXKlZVevXop3377rWKxWPJ9nQtj/zpGRkYqinL9feDv7+/wOuXatm2b8vTTTysRERGKt7e3otfrleDgYOXee+9V5s2bp2RnZ+e73wsXLihjx45VWrRooQQEBCh6vV7x9PRUwsLClNatWyvPP/+88tVXXylxcXFFam9eBb0+RanH/n+joLRhwwbbdmlpacoHH3ygdO7cWalYsaJiMBgUg8GgVKpUSWncuLEycOBA5aOPPlKOHj1a2MuSr+zsbGXx4sVK7969lZCQEMXd3V1xc3NTKleurPTs2VOZN2+ekpmZ6XTb/J6XS5cuKSNGjFCqVaumGAwGJSAgQOnZs6eyc+fOG27nhAkTCnyublcdiqI4/H/Yv/ahoaEFbr9w4UKHdqxfv15VZuzYsar1Wq1WOXPmjG395cuXHeqYNGmSqo5z5845lJk2bVqxjzfXokWLlLvvvlvx8vJSvLy8lObNmyufffaZYrFYHJ5bZ8/Bzp07lbffflt56KGHlAYNGiiVKlVS3N3dFYPBoJQrV05p27at8uabbypnz5512PZGvx9uZp+5cnJylC+//FLp0aOH7X8k97Opfv36Sr9+/ZSZM2cqe/fuzbeOAwcOKCNGjFAaNWqk+Pn5KXq9XgkKClLat2+vzJw50+nntP13W0HJ/vNICCGEEP9tEqQQQgghhFP79+93uKjQsGFDxWw257vNsGHDHLaZMWOGqkxJBSlGjhxZpAshLi4uysyZM522174t/fr1Ux566KF86+rYsaNiMpls25eVIEViYmKRt6lfv75y8eJF1fNwI/ss7CJ4kyZNVOtee+01p69B586dVeWeeuop1fpLly4p7du3L7Rdbdq0US5fvux0H4VxdrH+6aefdthH3tcpKSlJeeSRRwptV0REhNML8tu2bVO8vb2L9JwvXbq00PbaK40gRUxMjFK7du0ibfPss8/e0Gt18OBBpU6dOoXWX7VqVWXXrl1Fel7mzZuX72vh6uqqrFmz5obaWpaCFPaKG6To0aOHqnxgYKBDGWfBybyf/xaLRTEajar17733nqqOmJiYQt//RWEymZRHH3003/fHfffdp7z++uuFPgcPPPBAkd7PRqNR+eabb1Tb3uj3w83sU1EUJSMjQ7n77ruLVEfXrl0dts/KylJeeOGFQretVKmSQxBPghRCCCGEuFFahBBCCCGcWLNmjUPewIED0Wrz//nw5JNPOuStXbu2RNtVXCaTiVGjRhWpHcuWLeP777/Pd/369etZsGBBCbbu9jtw4AB9+/a95fuxfy8sXboURVFUeVeuXGHDhg35bpeUlESnTp3YuHFjofvbunUrXbt2JS0t7cYbfc22bdv4/PPP812fk5PD/fffz/Llywut6/Dhw3Ts2JHLly+r8p999lmSk5Nvuq1lyaRJkzhy5Mgtq//06dN06NCBf/75p0hlO3fuzOHDhwst+/TTT+f7WmRnZ/PMM89gNpuL3d5/k127dqmWIyIiHMpERESg0Wjy3U6j0TB06FDV+g8++IDNmzeTkZHBuXPnGD58uGp9pUqV6NmzZ7HbO3nyZJYuXZrv+t9++40PPvig2PXmJz09nSeeeKJI781bvc85c+awbdu2G6538ODBfPjhh4WWu3jxIl26dCnS/5gQQgghRGEkSCGEEEIIpw4ePOiQ16xZswK3adiwIa6urqq8Q4cOlWi7chmNRrp3786cOXNYvXo1e/bs4fjx4+zevZu5c+c6zPvw/vvvF1qnoiiUK1eOxYsXc+jQIebMmYNer1eVWbJkiarO06dPM3LkSFWZSpUqcfr0aVV6+OGHi3RcueUrVaqkyh85cqRDnXmFh4fz0ksv8d1337F582aOHDnCwYMHWbNmDQMHDlSV3bp1Kzt37rzpfRbk8ccfx83NzbZ89uxZtm/friqzbNky1cXfOnXq0KpVK9vyhAkTVBfgvLy8mDlzJlFRURw8eJC5c+fi5+dnW//333/z7rvvFrmN+ckdd3/EiBH8+eefHDp0iOXLl9O8eXPAehFw06ZNtvJ6vZ4JEybw119/cfjwYb7++mtCQkJs6y9evMirr75qW05ISFD9fxkMBj7++GMOHDjAsWPH2LFjB19++SVPPfVUkeYouBW++eYbTp8+zUMPPaTKb9GihcN7Ind+hLzPCcCLL77Irl27OH78OFFRUSxfvpyXX36Z+vXrO1zMLooXXniB2NhYVd4zzzzDli1b2Llzp8P8N0lJSQwbNqzQehVF4bHHHmPXrl1s27aNyMhI1Xpn790b1aFDBzQajdP04IMP3rY6iiMjI4MrV66o8sqVK+dQTq/Xq/4fAYfPjKlTp/Lss8/aXv+LFy8SGRmJ0WgkNDRUFfgLDw9n9erVeHh4FKu9sbGxTJs2TZVnNBqZM2cO+/fv56effqJ27dqkp6cXWpevry+PPPII8+bN4/fff2ffvn0cO3aMnTt3MnPmTNWE3tnZ2arAx41+P9zMPsHx//Cxxx5j+/btHD9+nP379/Pjjz8ybtw4WrRo4XDTwY8//qj6jgPr/922bds4cuQIK1asoH79+rZ1KSkpPPfcc7blrVu3cvr0aVq0aKGq46GHHnI45n/D3ExCCCGEKEGl3JNDCCGEEGVUt27dHIZnKMo48sHBwQ5DUuRVUsM9Fea7775Tbe/h4eEwVJWzIW1Wr16tKjN8+PBChzkpytjmeRU03FNxyhRHvXr1VPU5G+e9OPssynBC9sOtDBs2TLW+RYsWqvXTp0+3rcvMzFQ8PDxU65cvX+7Qjnnz5jm8PsWdn8LZsCyjR4/Ot3z16tXzbXeutWvXqsrodDolMTFRURRFuXLlimpdnTp18m2z2Wx2GPv9dgz3VJwyueyHYYqOjs63bHJycr7rnHE2V0H//v0dyj377LMO5fLOS+PseWnVqpXq+Xc25NBHH31UrPYqivOhmgpKDzzwwC2pw5niDPfkbC6JAQMGOC1buXJlVblatWo5lDGbzcqHH36o6PX6fI+jT58+SkpKSpGOxd7HH3/sUN9XX32lKnPp0iXFYDAU63Pbmffff19VR+3atR3KFPf74Wb3af/dXdC8Kvb/h506dVJtO3z4cIdtTpw44fD8HjhwQFWmKN/zQgghhBB5SU8KIYQQQpQoxW5In1s5TMq+ffsYNWoULVu2pFy5cri5udnuKLbvuZCWlkZiYmKB9YWHh9O1a1dVXu3atVXLhdVRWjIzM/n888958MEHCQ8Px8vLC51OZ3s+7HvGXLhw4Za3yX7Ip+XLl9t6KZw6dYo///zTtk6v1/PEE0/Ylnfv3u0wdNMjjzzicOf4U089pSoTFxd300Ou6PV6XnvtNafrLl68yMmTJ1V5r7zyikO7OnfurCpjNpttd+OXK1eOKlWq2Nb9888/NG3alJEjRzJnzhzWrl1r6zGg1WqLfSd5aWnatKlq+a677uKpp55i+vTp/Pzzz5w6dcq2zsvLq1h1298dDtZeFPaeffZZh7zNmzcXWPfw4cNVPTuCgoIICAhQlSmr//e3g/1nen55zvLte8ycOXOGpk2b8sILL5CTk5PvPr/99luqVq3qdNjBwuT9XAFwd3enX79+qrwKFSrQrVu3ItW3ZcsWhg4dStOmTQkICMBgMNj+z0ePHq0qW1KfqzezT/v/w+7du/PEE0/wzjvv8P3333PkyBHb65T3/9BsNrN161bVtnPmzHH4bKtRo4ZDewv7HxNCCCGEKIxLaTdACCGEEGWT/UU6sA6jUbNmzXy3MZlMDhfzgoKCSrxtAG+++SZvv/12vhfLnElNTXV6XLlq1arlkOfu7q5aLotj0584cYKuXbuqLgIXJjU19Ra2yKpTp06EhYVx5swZwPr++eOPP+jWrZvDkCI9evRQDSFz8eLFG97v5cuXnY6ZX1RVqlTJ931ys+3KNXPmTPr162d7P+3du5e9e/eqyjds2JChQ4fy9NNPFzgXTFkxfvx4Vq9eTVxcHGC9eDp//nxVmUqVKjFgwABee+01fHx8ilz3pUuXHPKqV6/ukFetWrUibZuXfSASHP/vc4NrN2vp0qW24bHsGY3G21ZHceQdXihXRkaG07L2+XlfY7PZTPfu3VVzGISEhPDOO+/QqFEj4uPj+eCDD2zzAsXFxdG9e3f27NlDw4YNi9xe+6GpKleu7DBsH0DVqlULrMdisfDkk0+ycOHCIu/7Zj9XS2KfI0eOZOnSpbZganx8PIsWLVKVCQgIoG/fvrz55psEBwfbymVlZd1Qu+3n3BFCCCGEKK6yf7YjhBBCiFJRr149h7w9e/YUuM3+/fvJzs5W5eUdv9oZZxf9cy9y5uf3339nypQpxQpQQP53/+ZydmFap9MVax+l4YknnihWgAIKfy5KgkajYdCgQaq8r7/+GsBhUltnk67fqPwuoBbVrZoHIm+7Hn74YXbt2sXAgQMpX7680/L79+/nueeec5hrwd6N/A/dCuHh4Rw4cIAxY8bkG8y8ePEi06ZNo2PHjiV24T+vG5nr4nb+3wcHBxMWFuY0OZvn4VbVURzu7u4O79GYmBiHcjk5OQ5B6ryBgNWrVztMsrx48WIee+wxIiIiaNu2LcuWLVMFi81mc7HnmSmsN0d+5ezNmzevWMGCklAS+wwMDCQqKorJkyfTsGFDp8cfHx/Pxx9/TPPmzbl69epN7Q9u/jNXCCGEEEKCFEIIIYRwqkuXLg55ixcvLnCbBQsWOOT16NFDteziou7I6Wzy0mPHjhW4H/sL3G5ubrz77rvs3r2bU6dOcfr0aadt+Tc6e/YsO3bsUOW1b9+eX3/9lX/++cc2SWmjRo1KpX2DBw9W9QL46aef2LFjh+pipbOhV5wFCn799VeHyVedpU6dOt1Umwu6QO2sXXPnzi1Su+wnMG/cuDELFiwgOjqa6Ohotm7dyhdffOEwWfXHH39MQkKCbbkk/oduleDgYN59912OHj1KcnIyu3fvZtmyZbz44ouqu9mjoqL49ddfi1yvs+fdftit/PIqVKhQ5P0I55o1a6ZaPnz4sMNF/oMHDzrk5d3uyJEjDvU2adJEtazT6RwC2/ZD1RXGPqBy/vx5p0NL5fbwyo/994yfnx+ffvop+/bts/1PT5kypVhtK0xJ7dPb25tx48axb98+0tLS2L9/P99//z1jx47F29vbVu78+fO2oEhAQACurq6qet58880ifba98cYbJXD0QgghhPgvkyCFEEIIIZxq0qQJbdq0UeXt2rWL2bNnOy2/ceNGPv30U1VeQEAA/fv3V+XZDx1iP39AQkIC33zzTYFtsx9y55577mHMmDE0bdqUqlWrEhYWxq5duwqsoyTZX9gpibtKi1qns+GHZs6cyX333Uft2rUJCwtDp9Nx9OjREttncYSEhNCxY0fbcmpqqkOviYEDBzoEBpo1a+YwdM1PP/2U7x3kYWFhaDQa/vnnH4ehekpS5cqVHYYU+vHHHwkJCcm3XUajkT179qjGf7cfgqh8+fLcfffdDB48mO+++85hmJy8QQf7/6GTJ0+qejDl5OTw+eefl8ThFus9YX9MXl5eNG3alD59+jBr1izuu+8+1frizB3Srl07h7y5c+cWKc/ZtqJ4HnnkEdVyfHw8GzZsUOV9++23qmWtVqsKuDkbcsl+iDOz2ewQlChur5YWLVqoljMyMhy+Uy5fvsyqVasKrMf+s3XAgAE8++yzNGzY0Pa/vXPnzkLbU5z/oZLYZ3R0tCpY5O7uToMGDejduzdvv/02gwcPVpXP/T/U6XS0bdtWte6XX36hfPny+X62+fv7s23bNvz8/G74mIUQQgghQOakEEIIIUQBZs6cSdu2bVXjVI8aNYqoqCieeOIJQkNDiY+P58cff2T27NkOw7fMmDFDddcmQIMGDWxjjoP1Asnw4cN55plniI+P57XXXiMlJaXAdtnPc7F+/XoWLlxIixYtbONvO7tYeavYtycmJoa5c+fSoUMH28WasLCwYtd5/Phx2/KKFSvo0aMHlStXRqPR4OnpSWBgoNM5PyZOnMjrr7+Ot7c3e/bs4a233irSRaKi7rO4hgwZwtq1a23L9ndUOxvqyWAwMGTIED788ENb3meffUZ8fDxPPvmk7fm8ePEie/fu5ddff2Xr1q0MGDCgyBPi3qjhw4fz8ssv25ZXrVpFly5dGD58ODVr1sTFxYXo6Gj279/PmjVrWLduHa1atVJdsG3UqBF16tSha9euNGrUiCpVqmA0GklISGDp0qUkJSWp9unp6Wl73KBBA9W6pKQkHn/8cV599VWys7OZPHlyoXeJF5X9+2vfvn18//33NGzYEBcXF1xcXKhcuTIAI0aM4PDhw3Tv3p0WLVpQrVo1vL29ycjIYPPmzar3gP0xFSYkJITu3burel98/fXXeHh48MQTT6DX6/nmm28c/u8jIyNvan6Ssig6OrrA19ff39/hczc6OprMzEzbsv08BiaTyaHOvJ9ZjzzyCOPGjeP8+fO2vEGDBjFnzhzCw8NZu3YtM2fOVG3/0EMPERoaalu2740B1ovw77zzDg0bNiQ+Pp7//e9/Dp8PzrYryCOPPMKoUaNU31tDhw4lJSWFtm3bcubMGcaMGVPo/Av2n4fLly8nMjKSunXrcunSJebMmcPKlSsLbU9xvh9KYp/vv/8+3333HT179qRVq1aEh4fj6+tLdnY2UVFRDr018v4fDhs2jHXr1tmW9+3bR9u2bXnppZeoW7cuRqOR2NhYDhw4wNq1a1m1ahVBQUE8/vjjBR7zunXr+P3336lRowZarRY3NzfbXBhCCCGEEAAoQgghRAEGDhyoALa0YcOGm64zNDRUVed/xZdffqk67gkTJpR2k4pk6dKlil6vV7W9KGns2LFO6zty5Iii1WoL3Faj0Tjk5fXdd98Vuv8KFSo45J0+fVpVT2RkpGr9wIEDHdpr/7o5e88eOHCg0PbkZf8/4Oy98PLLLxdYX9621qtXr8CyOp1OCQoKKvRYi7PPDRs2FPr85srMzFT8/Pyc1tm2bVun2yiKoiQkJCi1a9cu1vvO2XEVxv5zLjIyssDyWVlZSrt27YrVLvs6AwICirxt1apVFbPZbNs2MTFR8fDwKPb/kP3rU5Tj/uWXXwrcT2hoqK3sQw89VORjcnFxUY4dO1as1+nkyZNKYGBgkffh4+OjHDp0SFVHUd+3RfkfLcyECRMc9lXc71BndRSWZs2a5VCP/WddUZK933//XXFxcSnStsHBwcrly5dV21sslmL/37i6ujq8hkUxbty4Ir0H83svK4qivP/++4XW4ex7xl5xvh9KYp+FfY7bp/Xr16u279evX7G2t3/eFEVRPvzwwwK3KewzVoj/MvvP6/x+WwkhxL+NDPckhBAlQKPR5JuMRiPVq1fn8ccfZ/369aXdVPEvdfXqVSZOnGhLJTkfQ79+/diyZQt169YtUvmgoCC+/vpr3n77bafra9Wqxbhx4/LdvmbNmowfP77AffTu3Zt+/frlu75hw4bMmTOnSO0tCfXq1eP+++8v0Tqff/55h7uh8/PFF1+ohhLKS6fT8cknnxTpbvLi7LM4DAYDjz32mNN1Q4YMyXc7Pz8/1q9frxouqiAajcZ2V/+t5Orqyi+//ELfvn2LvE2VKlVuaF/+/v4sWbJENa+Hr68v//vf//KdEDg4OJgZM2bc0P7s3XvvvQ7zBtwsnU7H//73P8LDw4u1XbVq1diwYQO1a9cutGxYWBhr16791/WiKE1dunRxGIrMmZo1a7Jp0yaHO+U1Gg3fffcdrVu3LtL+/Pz8+P7772/oNZwwYQJ9+vTJd32rVq0YMWJEgXU8//zzREZG5ru+c+fOTJgwodC2FOf7oaT2WVSvv/46HTp0UOUtXLiQF154ociT0Dv7bBswYAAhISEl0kZxXWZmJh9++CEdOnQgKCgIvV6Pj48PVatWpWXLljz77LPMnTuX6Ojo0m6qKKM2btyoOl/Yt29faTdJCCFsZLgnIYS4xTIyMjh16hSnTp1iyZIlPP3008ydO7fIJ3+lzcfHRzUJpf04wzciKChINeyEuHlXr17lrbfesi1HRkYyaNCgEqu/RYsWHDhwgNWrV/PLL7+wY8cOLl68SGJiomqIJ61Wy6pVq2jatGmB9b311lvUqlWLjz76iL///htFUahRowZ9+/blpZdeYtmyZQVur9FoWLJkCR07duTzzz/n0KFDaDQaqlatSt++fRk9enSRxgovSd9++y3vvvsu3333HSdPnnQ6mXFx5I49PmXKFDZt2kRMTIzTyV/BOhxKVFQUU6ZM4Y8//iA2NhZ/f39at27NK6+8QqtWrfj6669LdJ/FNWTIEIfAkZeXl8NY9/YqVKjAunXrWLt2LUuWLGHnzp1cvHiRtLQ0PDw8qFSpEvXq1aNdu3b07NlTNbzMreTt7c0333zDqFGjWLhwIdu2bePs2bOkpKTg5uZGhQoViIiIoG3btnTv3p06deqott+0aRNbtmxh69at/PPPP8TExBAbG4uiKPj5+VG7dm3uuecenn32WQICAhz2/+STT1KpUiWmT5/Orl27yMrKIiwsjAcffJBXX32V/fv3l8hxuri4sG7dOqZMmcLKlSs5c+ZMvsPk/O9//6N3795s27aNqKgorly5QmxsLJmZmXh5eVGtWjXatWvH008/7fB8FFW9evX4+++/WbZsGT/88AO7d++2PW8BAQE0adKEBx54gP79++Pm5nYzhy6ceOCBBzh+/Dhz5szht99+4+TJk6SkpODv70+DBg146KGHePLJJ53OPwHW7/+tW7fyyy+/8N133xEVFcX58+dJS0vD1dWVgIAAIiIi6Nq1K4MGDcLf3/+G2uni4sI333xD9+7dmTt3ru17pmbNmjz++OOMGDEi30B6LoPBwO+//87s2bNZvHgxx44dw9XVlVq1avHEE08wbNgwFi1aVKT2FPX7oST2+frrr9O6dWu2bdvGrl27uHz5MjExMbbPzLCwMFq1asXgwYMd5u8A6++8//3vfwwdOpT58+ezZcsWTpw4QXJyMq6urpQrV446derQunVr7r33Xu666y6HOnx8fNi+fTuTJk3ijz/+4OLFi6p5c0TxXbp0ic6dOzvM5ZOcnExycjJnzpzhzz//BMDDw8NhPjAhwBqkyHu+EBYWRqNGjUqvQUIIkYdGUfLMqiWEEOKG2AccAgMD0el0ZGdnk5iY6FD+3XffZcyYMbereaKMWLBggWrCygkTJjBx4sQSqfvMmTNUrVrVthwZGcnGjRtLpO7CTJ8+XfV+rlatGjt27KBcuXK3Zf9CCCGEEP9mnTt3Vs0XAtaAkqenJ8nJyaobRhYtWiRBijtY+/bt2bRpk2359OnTxZ7XLD8TJ05UBSm+/PLLEr2pSQghboYM9ySEELfArl27iI6OJiEhgTNnzjgMbTB9+nQsFksptU6IkvXKK6+ohuw5deoUPXv2vOmeBEIIIYQQ/3UnTpxQBShq1qxp60EXHx9PRkYGf//9N++//36JD88nhBBC3C4SpBBCiFssNDSUjz/+WJUXFxfHsWPHbMt557AICwtDURQ+/fRTmjZtiqenp0NPDUVR+OWXX3j44YepUqUKbm5u+Pj4cNdddzFlyhSSk5MLbFNUVBTPPfccdevWxdvbGzc3N0JCQujatSuzZ89WlR00aJCqffZ358fExDB27FiaNGmCr68vLi4u+Pv7U7NmTXr37s306dM5c+aMapuwsDBVnc6kpaUxe/Zs1bi7/v7+tGzZkkmTJhEXF+d0O2d1r1mzhi5duuDr64vRaKRFixasWLGiwOfImY0bN6rqHjRoEBkZGUyYMIGaNWvi5uZGxYoVeeaZZ25qPOC//vqLJ598kpo1a+Lp6Ym7uzthYWH069ePP/74w6F87jBHeW3atMnhfXUrffLJJ7Rv3151DI8++qgE44QQQgghbsLff/+tWh42bJhqmC0XFxfq16/Pyy+/zJ49exyGUXT2+9VeUX6b5+TksGjRIu6//34qV65sO/+oXbs2Q4YMYcuWLU6327hxI0888QTh4eF4enpiNBqpWrUqDz74IF999ZXTbfbt28czzzxD7dq1bdvUrFmT4cOHc+rUKafbWCwWvvrqK+69914qVKiAq6srHh4ehISE0LZtW8aMGeP0d/TPP//Mgw8+SEhICG5ubri7u1O5cmVatGjByJEj+f77753uLz/Ozp0OHz5Mnz59KFeuHO7u7ra50270d3J2djZffPEF3bp1Izg4GFdXV3x9fWncuDFjxozh/PnzqvILFixAo9GoelEADB48WNXWkurhLYQQN6T05uwWQoh/D0CVTp8+rVqflpbmUGbbtm1Otw8JCVEGDBjgUD5XcnKy0r17d4f1eVOVKlWUv//+26GdZrNZGTlyZIHb2n81DBw4ULVuw4YNtnUXL15UKlasWGh9H374oarO0NDQfPenKIqyf/9+JSwsrMA6AwIClHXr1jlsa1/3m2++mW8dixcvLvB1tbdhwwbV9r169VKaNm3qtO6KFSsqJ0+eVG3/5ZdfqspMmDBBtd5isSijRo0q9Pns16+fkpmZaduusPKAEhoaWqxjvRHx8fHKxIkTlQkTJtjS/v37b/l+hRBCCCH+rZYuXar6TXfPPfcoSUlJRd7e/vfrwIEDHcoU9tv85MmTSsOGDQv8rWlfb1pamtK3b99i/z598803FY1Gk+82BoNB+eabbxy2sz9ncZaaNm2q2mbChAmFbhMQEFDk59pZO9544w3Fzc0t39/0FotFtX1kZKSqjP155dmzZ5VGjRoV2Gaj0agsXbrUto39OUh+yf7cRAghbieZOFsIIW6DI0eOOOTlNxnkuXPnbJMienh4YDabVZNMP/bYY/z666+qbTw9PcnIyMBsNgNw/vx5unfvzr59+1T7eeWVV/jggw8c9unp6UlOTk6+k6HmZ+bMmVy6dMm2rNFo8PHxIS0t7YYn242NjaVbt26qegGMRqNq+KD4+HgefPBBdu/eTc2aNfOtb/LkyQC4u7uTkZGhWjdmzBj69euHTqe7obbm9sbQaDQYDAbV63Tp0iUef/xxtm/fXuRJ0qdOncrMmTNVeTqdDhcXF9Vr88033+Dt7c3cuXMBKF++PGazWdW7JLfnSa6goKDiH2Ax+fv7M2HChFu+HyGEEEKI/wr737m///47FSpUoG3btrRs2ZKWLVty99134+XldUv2f/XqVe655x5OnjzpsM7X15fk5GSnPQKeeOIJp70QvL29ycjIcHquMGPGDNtv91yurq5otVrb7+ysrCz69+9PSEgIrVq1AuDgwYMsXLhQtZ3RaESn05GSkuL0uBITE5k6daoqz83NDYPBQFJSktNtbsTbb79tqzsrKwslz7Sw33zzDZGRkTz33HNFqisrK4vu3btz8OBBVb79eVJ6ejoDBgygYsWKtGvXDnd3d8qXL09qaippaWm2ct7e3ri7u9uWPT09b+gYhRCiJMhwT0IIcYudOXOGYcOGqfICAwMLvLDu7+/PqlWrSElJIT09ne3btwPWk5KVK1faylWvXp3du3eTkpJCcnIyQ4cOta07f/48M2bMsC0fPXrUIUDx0EMPcfLkSdV+evXqVeRjO3DggO1xREQEly5dIjExkczMTC5evMiPP/7Ik08+SUBAQJHrnD59uipAUaNGDfbv309aWhpnzpyhRYsWtnUpKSm8+eabBdbn5+fH2rVrSUtLY+/evaq2XLp0if379xe5bc706dOHhIQEUlNTWbp0Ka6urrZ1O3fuZO3atUWqJzY21uFEacKECaSkpJCSksJnn32mCqZ8/vnnHDp0CIDo6Gh27dql2rZ169ZER0fbkv16IYQQQghR9jVp0oRmzZqp8tLT01mzZg1vvfUW3bp1IyAggAceeIA9e/aU+P5nzJihClAYjUY+/PBDkpOTSUxMJDExkYULF1K9enVbmXXr1jkEKIYOHcqlS5dISkoiLS2N33//nQ4dOtjWx8fHq4YbcnNzY9myZWRkZJCWlsaXX35pu/HHZDIxevRoW9m85yRgHcIpLS2N5ORkkpKS+Ouvv5gwYQINGza0lTly5IgqUPLRRx+RlpbG1atXSU1NZf/+/bz77rvcfffdN/jMWbm6urJ48WJSUlK4evUqjz32mGr91KlTbTeaFWb+/PmqAEW5cuXYuHEjqampxMbG0qNHD9s6k8nEK6+8AkDfvn2Jjo5WPWcAH3zwgep8wX69EELcVqXdlUMIIf4NsOsqGxgYqJQvX17x8/Nz2pX23XffLXD7jz/+2Ol+Bg8erCq3atUq1fqcnBzFaDTa1letWtW2btKkSaptGzdurJjN5kKPraDhnnr37m3Lr1+/vnLhwoUiPV8FdSm3H+Zp5cqVqvUHDhxQrXd3d1cNfWRf9+zZs1XbP/nkk6r1y5cvL1KbFcWxu7y3t7eSnJxcYP0jR460rStouCf7dXfddZfD/h9++GFVmbfeesu27vTp06p1kZGRRT4uIYQQQghRdp0+fVqpU6dOocP1uLi4KEuWLFFte7PDPVWrVk21bubMmYW21/738P3331/oNgsXLlRt8+qrrzqUueeee1Rlzp49qyiKovz8888O5w/2wyjZ+/vvvx3Ov0wmU6HtLIz9udPgwYNV61NSUhQfHx9Vmb1799rWFzTck/26jz76SFV3bGys4u7u7vQ5UhTH4a2+/PLLmz5eIYQoKdKTQgghboG4uDiuXLlCYmKiw7ohQ4bY7mrJT//+/Z3m20+c161bN9VkZ3q9XtXV9/Tp0yQkJAA49Bjo378/Wu3NfQ307NnT9vjAgQNUrlyZChUq0LFjR55//nmWLVumak9hUlNTHSbZ7tSpk2q5Xr16lC9f3rackZHBiRMn8q3z/vvvVy2XK1dOtZy3y3NxNW/e3KFrfceOHVXLhw8fLlJd9t22O3fu7FDG/rmwv2tMCCGEEEL8+4SFhbFv3z4+++wzIiMj0ev1TsuZTCaee+65EhuuKDU11WGi6oEDBxa6nf15R1G2sT/Peffdd1XnORqNht9//11VZvfu3QC0a9cOHx8fW36PHj3w9PSkSZMmPP7448yePdthuKq6detSrVo12/KwYcPw8PCgfv369OnTh2nTpjm06UbY/3739PR06BlTUucLgYGBNGjQQJUn5wtCiDuFBCmEEOIWc3Nzo2rVqjz66KOsXbuWefPmFThHQUBAQL5jyt7ICUfuPAX221apUqXYddkbOHAgr776qmqIo+joaDZs2MCcOXPo168fISEhrFmzpkj12bfRy8sLNzc3h3L28ysU9LxUrlxZtZy3rYBqXNjicjbPQ2BgoGo5v3Fw7dkfg7O6i3PcQgghhBDi38PV1ZWnn36ajRs3kpSUxJYtW5gwYYLqQjtAcnIymzZtyrceZ79985tLzv63ptFozHdevYK2K8p5x82c5/j4+LBixQrVftLT09m7dy9LlizhpZdeIjw8nOeff952/FqtlhUrVhAREWHbJisri4MHD7J8+XJef/11GjZsyEMPPVTsefvykvMFIYQoGpk4WwghboHTp08TFhZ2Q9sWNGFZ3juEwNoroLBJmXN/iPv6+qryz58/f0Pty0uj0TBt2jRefvll1qxZw759+zhx4gRRUVG2+uPj4xkwYAAXL17M966vXPbHl5KSQmZmpkOgIjY2tsDt8rLfZ1EnsS6KvBNV55fn7e1dpLrsj8H+GJ3lFXTcQgghhBDi38nd3Z02bdrQpk0b28X0o0eP2tZHR0fbHtv/9s3OzlYtm0wmrly54nQ/9ucP6enpJCQkFBqocHbeYd97wJ7971pfX18MBkOB2+T9nd+hQwdOnTrF5s2b2b59O0ePHuXIkSNERUVhsVhQFIU5c+bQuXNnHnzwQQAaNGjAwYMH2blzJ5s3b+bIkSMcO3aM3bt3256nH374gU8++YQXX3yxwLbkp6TPF+Lj423LsbGxDq+FnC8IIe5U0pNCCCHuIPbdd5csWaKa7Mw+Xbp0iVq1agGoJooD+Prrr7FYLCXSrqCgIPr378/777/Pjz/+yLlz51STwsXGxjp0T3bG09PTIbizbt061fLBgwdVJ1Lu7u7UqFHj5g7gBv3111+kpqaq8tavX69arlOnTpHqqlevnmrZ2YTb9s9F/fr1bY/th+4q6gR8QgghhBCi7Dp16pTDMEd5GQwG2+/9XHkvehuNRtW6y5cvq5Z/++23fH83enh4qCbEBli0aFGhbbY/7yjKNvbnOc8//3yh5zmDBw9WbePi4kLHjh0ZN24cixYtYteuXaxatUpVZsOGDapljUZDq1atePXVV/nyyy/Ztm2bwxBJ9tsUh/25QWpqKrt27VLlldT5QlxcnMMQVXK+IIS4U0iQQggh7iB9+vRRLQ8ZMoQ//vhD9QPzwoULfPvttzz++OMMHz5cta1Op7MtR0VF8eijj9rmgFAUhaioKPr27Vvk9rz99tu8/PLLbNy4keTkZFv+lStXHMZ9za8bub2HH35YtfzSSy/ZfmyfPXuWp556SrW+R48ehd5ldaskJSXxzDPPkJSUhNlsZtmyZSxevNihfUXRvXt33N3dbcu7d+9m4sSJZGRkkJOTw+eff86KFSts6zUaDb1797Yt298ldeTIEWJiYm7ksIQQQgghRBlx6dIlunbtSqNGjZg1axYHDx603WiUk5PD119/7TC0apMmTWyP7YeD2r59O3/88QcA+/btY+TIkQXu/9FHH1Utv/HGG3zyySe2G3XS0tL49ttvmTJlSr7b/Pjjj4wYMcJ2o5HJZGLz5s2q3/Xdu3fHw8PDtjx9+nQ+++wz1fxxiYmJrF27lpdeeolWrVrZ8g8ePMiDDz7IkiVLVL3Fc3JybPNW5M0D6+/4Tp06MX/+fE6cOGF7Ti0WC3/++afTbW7EokWLWLp0KWazmeTkZJ599lnVEEwhISEOAZr8PPLII6rlSZMmsXnzZhRFIS4ujsGDB5ORkWFb36xZM0JCQmzL9ucLW7duLbGb1oQQ4qaV4qTdQgjxrwGo0unTp294+9DQ0ALLdu/e3WF/Op1OCQgIUAwGgyp/4MCBqm1ffvllh20BxdPTU3Fzc7Mt5zVw4EBV2Q0bNtjWjRw5UrXOy8tL8fPzUzQajUP9qamptu1CQ0NV6/OKiYlRKlSo4NBGDw8Pp+0+cuSIavuC6lYURZkwYYJq/Zdfflng853Xhg0bVNtqtVoFUDQajeLu7u7QvpYtWyoWi8W2/ZdffqlaP2HCBFX9U6ZMcfra2r+ugPL00087tC8kJMRh26CgIKV8+fLKlClTinycQgghhBCibNiyZYvT34f+/v6KTqdzWNehQweHOlq0aOFQztlvV2e/n69evapUr17daTk/Pz9bG+zPOx566CGn23h7eyuurq5Oz3umT5+e7348PT3zPWfau3evap3BYFACAwNt+8mbFixYoCiKoiQmJqry9Xq9EhAQoDonyk0TJ04s8utlf+6Ue77g5uZme5w3ffLJJ6rtIyMj8z2vzMjIUOrVq+dQh9FodMhzcXFRNm3apKp7/fr1Tt8H5cuXV8qXL68cP368yMcphBAlTXpSCCHEHWbp0qX07NlTlWc2m4mPj3eY1M1+Au733nuPESNGONSZmppKZmbmTbctJSWFxMRE1YR8Wq2W//3vf6o7owoSFBTEqlWrCA0NVeXnvYsKrBOM//TTTw7d22+nhx9+mDZt2qAoiuquJYCKFSuyePHiYs2BMXbsWF566SVVntlsdnhd+/Xrx4cffuiwfd6eM7nbxsbGcuXKlSJPyCeEEEIIIcoOZ3O6mc1mEhISHIbrqVWrltOhlaZPn+5QT+5v18cff5zKlSvnu38fHx9+//13p3f7JyYm5jtk0FdffeXQCxysE3vbz4uRa/To0bz55psOwxIlJiY6DLFqf56TV1ZWFnFxcQ776dKlC48//rjTbXJycoiPj3c4J2rYsCGjRo3Kd1+Fefvtt/Hw8CAzM9Oh10K/fv149tlni1yXm5sbv/76q8NwWunp6apld3d3vvrqK9q1a6fKb9euncOQURkZGVy5coUrV65gMpmK3BYhhChpEqQQQog7jJeXFz///DOrVq3i0UcfpWrVqri7u6PX6ylXrhxt27bl1VdfZdu2bQ4XsrVaLR988AG7d+/mmWeeoXbt2nh6euLq6krlypW55557mDlzZpHbMmrUKD7++GP69OlDREQEQUFB6HQ6jEYjtWrV4sknn+Svv/5yGC+2MA0bNuTgwYPMmjWLyMhIAgICcHFxwcfHh+bNmzNx4kSOHDlCx44di1VvSXN3d2fdunVMmjSJmjVrYjAYCA4O5umnn2bPnj0OY/gWRqPRMHPmTP78808GDRpE9erVcXd3x2AwUKVKFfr06cPq1atZunSp0yGuXnnlFf73v//RqFEj1dBRQgghhBDiztSiRQvOnDnDxx9/TP/+/WncuDH+/v7o9XpcXV2pUKEC99xzDx9//DH79u2jUqVKDnW0bduW9evX07FjRzw9PfHw8KBly5YsXLiQxYsXq4aEdaZatWrs2rWLhQsX0qNHDypWrIirqyteXl7UqlWLwYMHM2TIENU2RqORZcuWsX79egYMGED16tUxGo24ubkRGhrK/fffz6RJkxz2NWnSJPbv38/w4cOpV68eXl5e6HQ6fH19adKkCc8++yw//vgje/bssW1Tp04dfvjhB0aMGEHz5s2pUqUKbm5u6PV6KlSoQNeuXfniiy/47bffcHFxAazzdvz222+MGTOGu+++m9DQUDw8PHBxcSEoKIj27dsze/ZsduzYUWBApDAtW7a0DakbFBSEwWCgfv36fPTRR3z99dfFuqEJrMND/fXXX8ybN4+uXbtSrlw5XFxc8PLyomHDhowePZojR444DLkFoNPp+OOPPxgyZAiVK1e2PRdCCFEWaJS8t7sKIYQQIl8bN26kQ4cOtuWBAweyYMGC0muQEEIIIYQQoswYNGgQCxcutC1v2LCB9u3bl16DhBDiDlEme1IsXryYZ599lrvuuguDwYBGoynwIlBycjKjRo0iNDQUg8FAaGgoo0aNUk3iam/JkiU0b94cDw8P/Pz8uO+++xwmVBJCCCGEEELceeR8QgghhBBCiDtHmQxSjBs3js8++4yzZ89SoUKFAsumpaURGRnJrFmzqFWrFi+99BIRERG2IULsxzAHmDp1Ko8//jhXrlzhueeeo0+fPmzbto27776bjRs33qKjEkIIIYQQQtwOcj4hhBBCCCHEnaNMBinmzZvHmTNniI2N5bnnniuw7Hvvvce+ffsYM2YMv//+O9OmTWPVqlWMHz+effv28d5776nKHz9+nAkTJlCzZk3+/vtvZsyYwdy5c9m+fTsuLi489dRTMlmQEEIIIYQQdzA5nxBCCCGEEOLOUSaDFJ07dyY0NLTQcoqiMG/ePDw9PRk/frxq3euvv46fnx/z588n77QbX375JSaTiTfeeAMfHx9bft26dXniiSc4efIk69evL7mDEUIIIYQQQtxWcj4hhBBCCCHEnaNMBimK6vjx41y6dIm7774bDw8P1To3NzfatWvHxYsXOXHihC0/t/v1Pffc41Bf165dAdi0adOta7QQQog7Vvv27VEUxZZk0mwhhLizyfmEEEKIkrRgwQLV+YJMmi2EEEVzxwcpAMLDw52uz83PLZf72NPTk+Dg4CKVF0IIIYQQQvw7yfmEEEIIIYQQpc+ltBtwM5KSkgBU3azz8vb2VpXLfVyuXLkil7eXlZVFVlaWbTk8PJz09HRCQkKK13ghhBBCCCFug3PnzuHh4UF0dHRpN6XMkfMJIYQQQggh8ne7ziXu6CBFaXjnnXd46623VHlarVYmxxMFMpvN6HS60m6GKMPkPSIKIu8PURh5j4iCZGdnl3YTRB7OzifAwOHDUDE4GU9vY6m0S5Rt8jkvCiPvEVEQeX+Iwsh7ROTndp1L3NFBitw7nvK7Uyk5OVlVLvdxccrbe/311xk1apRtuUWLFpjNZg4dOoRWe0ePniVuEYvFQmxsLEFBQfIeEU7Je0QURN4fojDyHhGFqV+/fmk3ocwqK+cTR45ogUM83ONzZs0dUqxjEP9+8jkvCiPvEVEQeX+Iwsh7RBTkdp1L3NFBisLGfHU2xmx4eDg7duwgOjraYRzZwsakBTAYDBgMBtuyVqvFYrGg1WrlH1nkS6PRyHtEFEjeI6Ig8v4QhZH3iBA3pqycT+Taf8Ii/8fCKfmcF4WR94goiLw/RGHkPSJK2x39zgsPD6dixYps27aNtLQ01brMzEw2b95MxYoVqVGjhi0/MjISgN9//92hvjVr1qjKCCGEEEIIIf69ytr5xKlo/xvaTgghhBBCiDvZHR2k0Gg0PPXUU6SmpjJp0iTVunfeeYfExESeeuopNBqNLX/w4MG4uLjw9ttvq7ppHzp0iK+++orq1avTsWPH23YMQgghhBBCiNJR1s4nYhLDbmg7IYQQQggh7mRlcrinefPmsXXrVgAOHDhgy9u4cSMADz74IA8++CAAY8aM4eeff+a9995j7969NG3alP3797Nq1SoaNWrEmDFjVHXXrFmTiRMnMm7cOBo0aMDDDz9MWloaS5cuJScnh88//xwXlzL5tAghhBBCCCGK4E49n8i6WpPMqwm4+UqPCiGEEEII8d9RJq/Gb926lYULF6rytm3bxrZt2wAICwuznVR4eHiwceNG3nrrLb777js2btxIcHAwL730EhMmTMDDw8Oh/jfeeIOwsDBmz57NJ598gqurK61bt2bSpEk0a9bslh+fEEIIIYQQ4ta5Y88nsnzYvnYFHR/udeN1CCGEEEIIcYfRKIqilHYj7mR169bFZDLxzz//yOQywimLxUJMTAzlypWT94hwSt4joiDy/hCFkfeIKEzdunUB63BEouypW7cuhw8DWF+fN0fOZNLsUaXaJlG2yOe8KIy8R0RB7rT3h8lkIicnp7Sb8Z9isVhISEjA39//jniPiJLj6uqKTqcrsMztOpcokz0phBBCCCGEEOK/aM9RuTAjhBDiv0dRFC5evEhiYiJyP/XtpSgKiqKQkJCgmodL/PtpNBr8/PyoVKlSqb/2EqQQQgghhBBCiFJlsT06Fu1Tiu0QQgghSkdiYiIJCQkEBwfj6elZ6hdM/0sURcFisaDVauV5/w9RFIXU1FSio6MxGo34+5funGgSpBBCCCGEEEKIUqTV5WAxWx9fvhpSuo0RQgghbjNFUbh8+TK+vr6UK1eutJvznyNBiv8uo9FIZmYmly9fxs/Pr1RffxloTAghhBBCCCFKkYuLyfY4LakWWQmxpdgaIYQQ4vYymUyYzWZ8fX1LuylC/Of4+PhgNpsxmUyFF76FJEghhBBCCCGEEKXIVX99uCeuhnFox9rSa4wQQghxm+VeHHVxkQFfhLjd9Ho9gAQphBBCCCGEEOK/zM1Nd31B0bF+29HSa4wQQghRSmSoISFuv7LyfydBCiGEEEIIIYQoRe5GV9XyX0cyS6klQgghhBBC3H4SpBBCCCGEEEKIUmRQxyg4fNm7dBoihBBCiBK3bNky6tati7u7OxqNhn379hVr+3HjxtGjRw8qVaqERqNh0KBB+ZY9deoUvXv3xtfXF09PT7p06UJUVJRDueTkZN544w1q1qyJh4cHISEh9OnTh0OHDqnKpaSkMGbMGO655x6CgoLQaDRMnDixWO2/ldq3b49Go8k3RUdH28quXLmSJ554gvr166PX64vVg2Djxo2qenfv3u1Q5qeffiIyMhJvb288PDyoW7cun332mUO5tWvX0qpVK4xGI4GBgQwaNIiYmJgitSMlJYURI0ZQqVIlDAYDNWvW5L333sNsNqvK/fjjj4W2t6yRIIUQQgghhBBClCKtFty9LtmWLyRVKcXWCCGEEKKkxMbGMmDAAKpXr87q1avZsWMHNWvWLFYds2bNIj4+nvvvvx9XV9d8y8XGxtK2bVuOHTvGF198wbfffktmZibt27fn6FH1UJI9e/Zk9uzZPP3006xcuZKpU6eyb98+WrVqxdmzZ23l4uPj+eyzz8jKyuLBBx8sVrtvh48//pgdO3ao0rp169Dr9bRs2ZLg4GBb2RUrVrBz504iIiJo2LDhDe1vzpw57Nixgzp16qjyp02bRu/evalXrx7ffvstP//8M8OGDSM7O1tVbtOmTXTr1o3y5cvz008/8cEHH7B27Vo6depEVlZWgfs2mUx06dKFxYsXM3bsWFauXEnPnj157bXXeOmll1RlIyMj2bFjB+PGjbuh4ywNMiONEEIIIYQQQpSy8kGXOZNSEYCk5HCy467gGli+lFslhBBCiJtx7NgxcnJy6N+/P5GRkTdUR0pKClqt9T7zRYsW5Vtu+vTpxMbGsn37dkJDQwFo06YN1atXZ/z48SxbtgyAEydOsHnzZsaNG8crr7yCoihYLBbCw8O5++67+eGHH2wXvUNDQ0lMTESj0RAXF8e8efNu6BhulYiICIe8hQsXkpOTw1NPPaXK//zzz23P4/PPP8+ePXtuaH8tW7ZU5e3Zs4c33niDd955hzFjxtjyO3Xq5LD9K6+8Qs2aNfnuu+9sE8VXrVqVu+++my+++IKhQ4fmu+/vvvuOP//8k++//57evXsD0KVLF1JTU5kzZw7Dhw+nVq1aAPj5+dGyZUuOHDlS7GMsLdKTQgghhBBCCCFKWY2qeeahiKvNsV2/l15jhBBCCHHTBg0aRJs2bQDo27cvGo2G9u3b29bv3r2b+++/H39/f9zc3GjcuDHffvutQz25F9YLs2LFCjp27GgLUAB4e3vTu3dvfvnlF0wmEwB6vR4AHx8f1fa+vr4AuLm52fJyhwsqKcePH+exxx6jXLlyGAwG6tSpw5w5c1RlcodWWrx4MaNGjSI4OBh3d3ciIyPZu3dvofuYP38+np6e9O3bV5Vf1OexuD766CMMBgMvvPBCgeUuXrzIrl27GDBggC1AAdC6dWtq1qzJihUrCtx+27ZtaDQaunXrpsrv0aMHFoul0O3LOulJIYQQQgghhBClrHFjX9auu7aQ5cuOPfuo121AqbZJCCGEKDVJSXDgQGm3wlH9+mB3cT8/b775Js2bN2f48OFMnTqVDh064O1tnXdqw4YN3HvvvbRo0YJPP/0UHx8fvvnmG/r27Ut6enqB8044k5GRwcmTJ+nVq5fDugYNGpCRkcGpU6eoWbMmoaGhPPDAA8yaNYumTZty1113ce7cOV588UVCQkLo169fsfZdVIcPH6Z169aEhIQwY8YMgoODWbNmDSNGjCAuLo4JEyaoyo8dO5YmTZowb948kpKSmDhxIu3bt2fv3r1Uq1bN6T6OHz/Oli1beOqpp/D09Lwlx2Fv8+bN1KlTh++//57Jkydz4sQJKlSoQP/+/Zk0aZJtiK6DBw8C1tfDXoMGDdi2bVuB+8nOzkar1dqCTLkMBgMAf//9d0kcTqmRIIUQQgghhBBClLI2kdWY/v715e2HU3m69JojhBBClK4DB6Bt29JuhaMtW+Ba74jCVK9e3TYcUXh4uGqYoGHDhlG3bl3Wr19vu6u+a9euxMXFMXbsWJ544oli3fmfmJiIoij4+/s7rMvNi4+Pt+UtX76c4cOH07FjR1tegwYN2LRpE35+fkXeb3GMGjUKLy8vtm7dagvWdOnShaysLKZNm8aIESNU+w4KCmLFihW2nhxt2rQhPDycd955h88//9zpPubPnw/AkCFDbskxOHPx4kViY2MZMWIEkydPJiIignXr1jFt2jTOnz/P119/DVx//vN7jfK+Ps5ERERgNpvZuXOnrYcOwNatW1X136lkuCchhBBCCCGEKGX16xpUywcue5RSS4QQQghxK504cYIjR47w+OOPA9YJkXPTfffdx+XLlx0mui6qgoZmyrtu6NChfP/998yaNYuNGzeydOlSXF1d6dixo2ri7JKSmZnJunXr6NWrF0aj0eGYMzMz2blzp2qbxx57TNXm0NBQWrduzYYNG5zuw2QysXDhQurWreswb8StZLFYSElJ4eOPP2b48OF06NCBKVOm8MILL7BkyRJOnDihKp/fa1TYsFqPP/44/v7+PPPMM/z5559cvXqVpUuX8r///Q+4dcNZ3S53duuFEEIIIYQQ4l+gShXQuWTYlk8nVSnF1gghhBDiVrly5QoAo0ePRq/Xq9KwYcMAiIuLK1adfn5+aDQap3fTJyQkANfv4F+9ejXz589n7ty5vPjii7Rr145HHnmE33//nYSEBCZOnHgTR+dcfHw8JpOJDz/80OGY77vvPsDxmIODgx3qCQ4OzrfHwG+//UZ0dLTDhNm3WkBAAGDtCZNX7twRUVFRqnL5vUbOeljkFRgYyOrVqwFo2bIlfn5+vPDCC8ycOROASpUq3cRRlD4Z7kkIIYQQQgghSplWCwFBF4m5XAOAxNRwcqIvoQ+uWMotE0IIIUpB/frWoZXKmvr1b7qKwMBAAF5//XV69+7ttEytWrWKVae7uzs1atTggJN5PA4cOIC7u7ttHod9+/YB0KxZM1U5X19fatSoYZs7oST5+fmh0+kYMGAAw4cPd1qmatWqquXo6GiHMtHR0baL/fbmz5+Pq6srAwbc3jm9GjRo4LStiqIA13s41KtXD7C+HrmBmVwHDhywrS9Is2bNOHz4MGfOnCEtLY3w8HD27NkDQLt27W7qOEqbBCmEEEIIIYQQogyoGpJGzGXrYyW+Fif2/EGd7gNLt1FCCCFEafDxKfLcD3eaWrVqER4ezv79+5k6dWqJ1durVy9mz57N+fPnqVLF2iMzJSWFH374gfvvv98290XFitYbIHbu3EloaKht+/j4eI4dO0anTp1KrE25jEYjHTp0YO/evTRo0MA2mXRBli5dyqhRo2zDIJ09e5bt27fzxBNPOJSNjo7mt99+o3fv3vkGMW6Vhx56iN9//51Vq1bx2GOP2fJ/++03tFqtLRhUqVIlmjdvzuLFixk9ejQ6nQ6wvg5Hjx7lxRdfLPI+w8LCAGsgZMaMGVSsWJFHHnmkxI6pNEiQQgghhBBCCCHKgAYNPfjzz2sLidXYd2iGBCmEEEKIf6G5c+fSrVs3unbtyqBBg6hUqRIJCQn8888/REVFsXz5clvZTZs2ERsbC4DZbObs2bN89913AERGRhIUFARYh49atGgR3bt3Z9KkSRgMBqZNm0ZmZqZqCKfevXszfvx4hg4dyoULF2jcuDGXLl1ixowZpKenM3LkSFVbV61aRVpaGikpKQAcPnzYtv/77rsPo9FYpGP+4IMPaNOmDW3btmXo0KGEhYWRkpLCiRMn+OWXX1i/fr2qfExMDL169eLpp58mKSmJCRMm4Obmxuuvv+5Q98KFCzGZTAUO9XT27Fl27doFwMmTJwFsxxEWFsZdd91VpOOwN3jwYObOncuwYcOIi4sjIiKCtWvXMmfOHIYNG6YKBL377rt06dKFRx55hGHDhhETE8Nrr71GvXr1GDx4sKqt1atXZ+DAgbbJwAHeeOMN6tevT4UKFTh37hxffPEFf/75J7/++ivu7u431P6yQoIUQgghhBBCCFEGtLq7Mp9/dm1B0bHtnwQeLdUWCSGEEOJW6NChA3/99Rdvv/02L774IomJiQQEBBAREUGfPn1UZSdMmMCmTZtsyxs3bmTjxo0AbNiwgfbt2wMQFBTEli1bGD16NAMHDsRkMtGqVSs2btxI7dq1bdt7enqyc+dO3n77bT799FMuXLiAv78/jRs35pNPPnGYdHro0KGqybSXL19uC6KcPn3adld/YSIiIoiKimLy5MmMGzeOmJgYfH19CQ8Pdxj+CGDq1Kns2rWLwYMHk5ycTPPmzfnmm2+oXr26Q9kvvviCsLAwOnfunO/+N2zYoAoEALbeBwMHDmTBggVFOg57er2eP/74g7FjxzJ16lQSEhKoWrUq06ZNY9SoUaqy7du357fffmP8+PH07NkTo9FIjx49mD59OgaDwVZOURTMZjNms1m1fWJiIq+++irR0dF4e3sTGRnJn3/+Sf0SGIastEmQQgghhBBCCCHKgAZ13VTLey/f2XfECSGEEP917du3t81NYK9BgwYsW7as0DpyAxJFUb16dVasWFFoueDgYD788EPAekHcYrGg1WptQyvldebMmSLvvzBhYWGqngEFcXNz44MPPuCDDz4otOzRo0cLLTNo0CAGDRpUpH3nx2w2YzKZbENn5fL39+fTTz/l008/LbSOLl260KVLlwLLhIWFOX3ffPzxx0VqZ26Qw2KxFKl8WaAt7QYIIYQQQgghhAD7OTJPJleCfC5sCCGEEEKI26tz587o9Xp2795d2k0p0E8//YRer2fIkCGl3ZQik54UQgghhBBCCFEGeHqCl3csKcnWsaVj0mqSc+k8+kohpdwyIYQQQgjnLBZLoXfs2/c8uNM0bdrUNp8FWIeuKsvat29/R7UXJEghhBBCCCGEEGVGlSpJHD5kDVIoCbU4uWcttSs9WcqtEkIIIYRwbtKkSbz11lsFlinO3BUFDZFVWry8vG54Yu3S4Ovre0e1FyRIIYQQQgghhBBlRkQ9A4cPXVuIq8XBI59R+34JUgghhBCibHrmmWfo0aNHgWUqVqx4m1oj7lQSpBBCCCGEEEKIMqJF83J8lzuHZqY/u05d4OFSbZEQQgghRP4qVqwoQQhx02TibCGEEEIIIYQoI+rXNaiWd1825FNSCCGEEEKIfwcJUgghhBBCCCFEGVGrlnr5aEpFKGPjMgshhBBCCFGSJEghhBBCCCGEEGVESAi4uGTZli9nhGM6d6b0GiSEEEIIIcQtJkEKIYQQQgghhCgjtFqoUDHBtmxJqM3JqHWl2CIhhBBCCCFuLQlSCCGEEEIIIUQZUqtOntO0uFocPrql9BojhBBCCCHELeZS2g0QQgghhBDiP0FRwGyG7GxryspSJ2d59uuzs8FkgpwcayrK4/PnoUqV0j56UQxNmvixds21hcRq7D+yi15r1oCXlzV5e1//6yKndEIIIYQQ4s4mv2iFEEIIIcSdw2y2XnjPzr5+Ed7ZcnEu4ud9nBsIKCw5K5u3HfZlch8XdGgayNFBjhayddcf5+iuLds9zi1T2HKcGQJv08sjSkaDuq7XFxQXlqcreE66F2MOtuSRDR454Kk14OXqjZebN15GPzw8/dD6+IJvnuTjo37s7Q2enteDHgYDaDSlcahCCCGEEEJIkEIIIYQQ4j/LYnF+YT+/C/6567KyIDPz+h3+uY8LysutM29AoJBlJSebHHMOOZYccsw5ZJuzbRfq7S/k21/UN2vArC3eX5OTgIDT/bhCjtv1PJPWWofJSX0FPbYPPCi36hrxcQlS3Glq1VIvH9bW4pV7juRTOguIvZasPLPAMxs8ssDzPHicvLacJ7jhnifg4W7RYtS5YXRxw1trxNvFA2+9B96u3vgYfPA2+uLm4YPGx9ca5MgNeuT+zQ16eHqChwfodLfkeRFCCCHuRMuWLWPSpEmcOnWKzMxM9u7dS6NGjYq8/bhx49i3bx979+7l0qVLDBw4kAULFjgte+rUKUaPHs369esxmUy0atWKd999lyZNmqjKJScn8+6777J8+XIuXLiAn58frVu3ZuLEidStW9dWbv369SxevJjt27dz/vx5fH19ueuuuxg/fjxNmza9kaejxG3YsIGpU6eyf/9+0tPTqVatGk899RTDhw9HZ/ebZO3atbz55pvs378fo9FIjx49eO+99yhXrlyB+9i4cSMdOnSwLe/atYu77rpLVeann35i5syZ7N27F7PZTFhYGCNHjuSZZ565JW2w9+yzz/Lpp58C8OOPP9KrV68C21vWSJBCCCGEEOJWUhTrRffcC/Z5k32e/bA/eYcFyuexJisL35QUNBoNZGdjyc4iJycLkymLHFM2OSbrco752gV/298cTFhUF9qL89g+KGD/1/5Cf46x6D0EbPuT65ziP6pmTbuM+FpOy+Un1WBNRWcB0q+lBKcl9GbwyQCfq+CTCb6Z4JNl/eudZQ1+2AIh6PHQuuHpYsTT1RMvox9env54eQXi5VsOD99yaAICrMENDw/HlBvwcHOTHh5CCCHuaLGxsQwYMIB7772Xjz/+GIPBQE2HL/qCzZo1iwYNGnD//ffzxRdfFLivtm3b4ufnxxdffIGbmxvvvPMO7du3Z9euXdTKcxdEz5492b17NxMnTqRp06acP3+eKVOm0KpVKw4cOEBoaCgAn3zyCfHx8YwcOZKIiAhiY2OZMWMGLVu2ZM2aNXTs2PHGnpgSsnbtWrp27Uq7du34/PPP8fDw4Oeff2bkyJGcPHmSDz74wFZ206ZNdOvWje7du/PTTz8RExPDq6++SqdOndi9ezcGQ+E/nubMmUOTJk2oU6eOKn/atGm88cYbPPfcc7z++uvo9XqOHDlCtl1P6ptpQ5MmTdixY4dD/ieffMJXX32lCkpERkayY8cOfv31V6ZMmVLocZUFEqQQQgghxH+Holgv7qenQ0aG9W9uyrts/zg3iJDnsSkznaysNDKzM8jMSSczJ5PMnAyyTJlkmrPIMmeRZc4my5JNthayXCBLZ/2brbv+OOvaxXn7x7ll8l02QpaXdTnvhX+LtvCnQQhRtnl7Q4UKFi5fvvYPfakZXA0B11Rr0mXDbb52n6ODOA9rKkLpaykFuOKwVpMFXqfBK9sa4PDKsv71zrqe55MJvlkafDHgozXiqzXi6+KJr6s3fsYAfL2D0PkFgJ+fNfn6On/s5laST4MQQghRLMeOHSMnJ4f+/fsTGRl5Q3WkpKSg1Vp/EyxatCjfctOnTyc2Npbt27fbggxt2rShevXqjB8/nmXLlgFw4sQJNm/ezLhx43jllVdQFAWLxUJ4eDh33303P/zwAy+99BJgvShvf4f/vffeS40aNZg6dWqpBykWLFiAXq9n5cqVeHhYf6R07tyZo0ePsmDBAlWQ4pVXXqFmzZp89913uFyb06tq1arcfffdfPHFFwwdOrTQ/UVERNCyZUtV3p49e3jjjTd45513GDNmjC2/U6dODtvfTBu8vb0d9q0oCo8//jihoaF06dLFlu/n50fLli05ciS/nrhljwQphBBCCFG6zGbrxX/7lBsgKGSdJT2N7MxUsjJSyc5KJzsrnaysdLKzM8jOziArJ4PsnCwyzZmkW7JIc1FI10OaHutfV/Vj+3WZLnmSB2T6WB+bJRjw72DRgEUPZj1YXMCixwV3XHBDf+1v7rIOA3rc0Slu6BQDOlzRKga0iisaxcX21/pYj0ZxAYsLGlzQWPRoFJ112aIHRXetjB6Ua+stLtfa4IJicbEuKzoUswtYdCgWHRaz9a9i1qKYdZjNOixmrTWZNFjMWszma39NGsxmDeev1oOg0n6iRXHVrq3l8uVrC4cftqZrXFwU3IwW3I0m3Iw5uBqzcXXPROeWic4tDY0hDfRpKK6pWHTJmLVXMWkTMWkSydImkOOSSLZLIpkuiWTqE8E1BVzTQGu5LcemaCDZzZouFlwSyLyWHHt4eGeCfzz4XQS/DPDLtAY3fLKu//U1ueDj4omPqxc+bj74uPvh4xmAj1cQrj7+14etsp+QPHe+Dn9/65wdQgghRDENGjSIhQsXAtC3b1/69u1LZGQkGzduBGD37t1MmjSJrVu3kp6eTp06dXj99dfp06ePqp7cAEVhVqxYQceOHW0BCrBe2O7duzeLFi3CZDLh4uKCXq8HwMfHR7W9r68vAG55AvzOhiDy9PQkIiKC8+fPF6ldeR0/fpwJEyawdu1akpKSqFatGs8//zzDhw+3lckd1mjRokVERUWxZMkSkpKSaN68ObNnz6Zx48a2snq9HldXV9zd3R2OJe9xXLx4kV27dvHOO+/YggMArVu3pmbNmqxYsaJIQQpnPvroIwwGAy+88EKB5W5FGzZs2MCpU6eYOHFikd8nZZUEKYQQQghROIvFGhhIS1OnPHlKSgrZaclkpCaSkZZERkYyGRnJZGamkpGZSkZWGhk56ddSBpnmLDIsWaRrLWToIcMFh7/pese8DP31HgXZRjB5lfaT8x+jABYdWos7rooHLhhxUYzX/yru6CxGdIo7WosbLoo7WsUNrcWAVjGgu3ZRX6sY0Fr01gv7Flc0iuu1C/l6sOitF/LNLmBxRTG7WC/Um/UoZh0Ws4vtr8Wkw2LSoVi0mE06LCYtJtO1C/QmLaYcDaZrF+tNOdaUY8L6NwcUJxNBmK6lzNv93AqRR0QEbNjgfJ3JpCE1WUdqsg4ouQvoBjcLRg8LbkYTBvcc9G7ZuLhlojNkoHFNR9GnYtYlY9ZcxaS5SrY2gSxtIpm6BLJcE8l2jQdDsjXoYUgGQwq4ZN6yXh+5gY4zBZYyAVevJfXFFPcc8IkD3wvWYat8M62BDt88KSAdylncKefmT5AxiHLeFfAICEYTVA4CAyEgwDH5+YGLnGoLIcTNSMpM4kDMgdJuhoP65erj4+ZTeEHgzTffpHnz5gwfPpypU6fSoUMHvL29AevF5XvvvZcWLVrw6aef4uPjwzfffEPfvn1JT09n0KBBxWpXRkYGJ0+eVA35k6tBgwZkZGRw6tQpatasSWhoKA888ACzZs2iadOm3HXXXZw7d44XX3yRkJAQ+vXrV+C+kpKSiIqKKnYvisOHD9O6dWtCQkKYMWMGwcHBrFmzhhEjRhAXF8eECRNU5ceOHUuTJk2YN28eSUlJTJw4kfbt27N3716qVasGwHPPPcfSpUsZMWIEY8eOxWg08ssvv7BixQreeecdW10HDx60PRfOnp9t27YV61jy2rx5M3Xq1OH7779n8uTJnDhxggoVKtC/f38mTZqEq6vrLWvD/Pnz0Wq1DB48+IbbX1bILychhBDiTqco1mBBSgqkpl7/m5bm0PvAlJ5KWmYyaRlJpGWmkpZtTenZ6aSZ0kkzZ5BmyiDVnEGaJYs0skgjhzStmbQ8vQ7s/+YGE2zXe92uJb9SfF7uADq06HHFoHjiiid6xRO9xRu94onLtaSzeKFTPNGZjWgt7mjMRjQmd2syu6PkGMjJ1KDXGa/1BtCjmFxQzPprF/fzXNQ3We/EN5tcbHffK2YtZpMWs1mLxWy9sG8xWy/qWy/0azCZwWzS2Oa1VhQNFuQivhC30tChsGCB9aP8dsnK1JKVqYV4F6wf4jdPp1OsQQ9jDnq3HPTXgh46fSoal1QUfTKKSxJml6vkXEtZLglk6BMwuSZZh7cypFzr7ZFqDX7os0qkbRnXvruiCw12Z2Dt83ER2Id7DgSlQeBxCNwPgekQkGH9G5huDWz46b3wd/PDzzMQP59gfPwroAssB0FB1uCGffL0lPk3hBAijwMxB2j7ZdvSboaDLYO30CakTZHKVq9enYiICADCw8NVQ/UMGzaMunXrsn79ettd9V27diUuLo6xY8fyxBNPFOvO+MTERBRFwd/f32Fdbl58fLwtb/ny5QwfPlwVaGjQoAGbNm3Cz6/gk7jhw4eTlpbGG2+8UeT2AYwaNQovLy+2bt1qC9Z06dKFrKwspk2bxogRI1T7DgoKYsWKFda597AOXRUeHs4777zD559/DkCLFi1Yv349jzzyCHPmzAFAp9Pxzjvv8PLLL9vqyj32/J6fvM9NcV28eJHY2FhGjBjB5MmTiYiIYN26dUybNo3z58/z9ddf35I2XL16lR9++IEuXboQEhJyw+0vKyRIIYQQQtxuFov1qlNKitOkJCeTlZJIWko8qamJxF+N4Zw5k/TsVFKzU0nNSSPNlE6qOZNUSyapmmzS9JDqak2qx3bBhOzcb373a+m/SsF6Qd/kjqvJG3eLH24WX9wUX1wVH1wVP/SKDy6KDzrFC53F/VqAwM2aLAYwGcDsCmZXFJMrikmPxaTHnKPHYtJhznHBlK0jJ0eHOUdLTraW7GwtOdkacrI11+bI1pBpkov9/2U6nfWGa/vkLF+nA73eefn88l1c4IcfSvsoxY2oWxcuXYI//7R+ZaSmXo8/5z7OjUvnjVHnptwObzk5pXscZrOGtBQ9aSn6azlFu/u0IHqDCXfvdFw909B7JKFxT0AxxGByjSFHH0eWPo5MfRyKIelaj44kcEsC93jr35uMBWTo4ZyvNeUv5Vo6B4BGsc614X8W/I6A/7XhqfwzrENV+Wfr8HfxJMDgR4BHIAFe5Qnwq4h/QBV0QeUxuLlBrVpQsSKULw/X7soUQghx5zlx4gRHjhzh/fffB8BkMtnW3XfffaxcuZKjR486TM5cFJoCAt551w0dOpQVK1Ywa9YsGjduzOXLl5kxYwYdO3Zkw4YNqiGj8nrzzTf5+uuv+fDDD2natGmR25WZmcm6desYOnQoRqPR4Zg/+ugjdu7cSbdu3Wz5jz32mKrNoaGhtG7dmg15upru2bOHXr160aJFC+bOnYuHhwfr169n3LhxZGZm8uabbxbp+SnoeSuMxWIhJSWFpUuX2nqhdOjQgbS0NGbPns1bb71FjRo1SrwNX3/9NZmZmTz11FM33PayRIIUQgghRFFZLNarQElJ1pScTPbVeFKuxpCaFEtKShwpaQmkpl8lJf0qKVkppGankmJKI9WUToolkzSyrT0TXB3nRMibZ9Fi/Zb2vZb+5dwsBtxNPhjMPhgsPriafXC1+OBi8cVF8cHFbA0WaC3eaMzeaBQjiskNTG5Ysg1YTK5Ysl2x5Ogx57hiznbBnKMnJ9uFnCwXTFk6srN0ZGXqyM7Skplh7SkAkH0tJZXqMyDyyr3onntR/mZTbn1FyXd20b+gx7nBBJ3O8bGzZfu823Hj9I4dt34f4tbw9oY8cyDekJwc56P15Q105A14FBQQSU+/Xld6eskc4w0dU5YLObHeEOsNVCjWtjoXC0bvTNy90zF4peDikYTWPR7FNQ6zawzZ+itkul4hXX+ZLLdocE8Et0RrcOMm5uxQNJDkZk2nnd6gasb6TZSEagArE3ifhvJpUGk9VE6+lsxGKukDqGwMpqJfCOUq1MClSihUrmxNVapYh56S3hlCCFHmXLlyBYDRo0czevRop2Xi4uKKVaefnx8ajcbp3fgJCdZ5nXLv4F+9ejXz589n+fLlPPzww7aJs7t27UrVqlWZOHEiX375pUM9b731FlOmTOHtt9/m+eefL1b74uPjMZlMfPjhh3z44YdOy9gfc3BwsEOZ4OBg9u/fb1sePnw45cuXZ8WKFeh0OsAaINBqtUycOJHHH3+catWqERAQYGuHvYSEBKe9G4oqICCA6Ohounbtqsrv1q0bs2fPJioqiho1apR4G+bPn09QUBAPPPDADbe9LJEghRBCiP+OzEy4etWaEhPh6lWUxERSE6+QkBxNYkosCWlxJGYmkpiVRIIphURzGglkkKjNIsElh0Q3SHSHJIO1p0K2/TepFvC8lu5wGgU80OOBKx4aA0atASM+uFn8MFgC0CsBuCoBuCj+aBVfsPiC2ReL2Rsl2x1zljumTDeyMwzkZLiSneFKZoaerHQdGek60tN0ZKRryMiAzCyN9Ca4CQaDcu0iu0Z1wd3VtWgX8Au6E99ZvsFwvX5nyX5d7nJB9eYua7VyTU2IkqTXW+eF9rn5DgwqFov1azU3kJGSAsnJ1//mJvtgR97y9gGT2zG0ldmkJSXBSEqCEQgs1rbuntm4eWbi6pGK1pgEhqtYXGPJdokhyzWaDP0VFPcEa1DDIwY8o61/XW6uO0vu3BvHA/Lmpl9L54FdaC1Q7iRU2A8VUqBiClTI1FFZ508V9/JU8QmhSrlwfCpXh0qVrgcyype3fvAKIUQZU79cfbYM3lLazXBQv1z9m64jMND6/fP666/Tu3dvp2Vq1apVrDrd3d2pUaMGBw44zuNx4MAB3N3dbfM47Nu3D4BmzZqpyvn6+lKjRg3b3Al5vfXWW0ycOJGJEycyduzYYrUNrEEUnU7HgAEDVJNk51W1alXVcnR0tEOZ6Oho28V+sB7Lo48+agtQ5GrWrBkWi4V//vmHatWqUa9ePcD6XNx3332qsgcOHLCtvxENGjRw2lZFUYDrE5+XZBv27t3L3r17efnll20Tod/pJEghhBDizpKVBQkJEB8PCQko8fEkx18kPuEiV5NjSEqN52pGAkkZV7mak0JSTipXlXSSyOKq3kySAa5eu5Pxqps12GDK/T1joCTnH71tPK8FETx17nho3XFXAnCzlMNVCcBV8cdF8cNF8UOHL1qLHxqzF+QYUbKN1mBCthvmLAOmTFdysvRkpuvJytCRka4lLU1DWhrEXbt4ZDaX9tGWDQbD9eTqmv/yjTx2dwc3N+uym5tjss/X6SzExsZQrlw5tFq5ui+EuD20WjAarSkoqGTqtFgcp1iyT8nJ1vsMrv0MUP1NTLRud6tkpLqSkeoKeAMVi7ydh3cmHv4puPlexcUzDo1bNBa3i+S4XSDD7RypbufI8rhgDWi4ZtxQ2yxa69wa0V6w19a5xAzEXkvWC05el6HKEQhJgirJEJKiJUTnT4hnJUL8q1K5Qi1cQ6pCtWpQvTqEhMgk4EKIUuHj5lPkuR/uNLVq1SI8PJz9+/czderUEqu3V69ezJ49m/Pnz1OlShUAUlJS+OGHH7j//vttc19UrGj9Dtu5c6dqWKf4+HiOHTtGp06dVPVOnjyZiRMnMm7cOIfJrYvKaDTSoUMH9u7dS4MGDWyTSRdk6dKljBo1yjYM0tmzZ9m+fTtPPPGErUzFihXZvXs3ZrNZFajYca0rceXKlQGoVKkSzZs3Z/HixYwePdpWdufOnRw9epQXX3zxho4L4KGHHuL3339n1apVPPbYY7b83377Da1WawsGlWQb5s+fD8CQIUNuuN1ljfzaEEIIUTosFuuQSXFxtqTExpISd5HYxAvEJ18hLi2WuMwE4nOSiLOkEkcG8a4m4owQZ4R4d+tfW5BBj3Wi5jI4WbO7RYenosfzWo8ED50bHi5GjC4euGkCcSUIFyUAF00gWgLRafzRmHyxZBlJu6rFoA3EnGkkO93aMyEzVU9Gmp70NC3JyRpSUiA2GU6nWp/a/wIXF+tF+tyL+vmlvBf9jUbrsru7+nFBee7u6kCCi0vZutP/v/J6CyH+/bRa6/zRnjfRG9FstgYqkpNtIzOqAhtxcY5/ExKs65OTS+5Y8kpLdiMt2Q0IAsILLOvpZcYvMAuvgDTc/ZIxeMehdbtEhuY4WW5nuKo/TqzbcbI8LoNLdrHbkmKAw+WsycoCxF1L+9EoUP44VNhr7ZFRIU1DsIsPFTyCqeAfSsXyNaga0oByVeujCQ2F4GDpiSGEEDdg7ty5dOvWja5duzJo0CAqVapEQkIC//zzD1FRUSxfvtxWdtOmTcTGxgJgNps5e/Ys3333HQCRkZEEXbtbYPTo0SxatIju3bszadIkDAYD06ZNIzMzk4kTJ9rq6927N+PHj2fo0KFcuHCBxo0bc+nSJWbMmEF6ejojR460lZ0xYwbjx4/n3nvvpXv37uzcuVN1HHknAy/MBx98QJs2bWjbti1Dhw4lLCyMlJQUTpw4wS+//ML69etV5WNiYujVqxdPP/00SUlJTJgwATc3N15//XVbmZdeeokRI0bQs2dPnn32WYxGI+vWrWPGjBl07tyZhg0b2sq+++67dOnShUceeYRhw4YRExPDa6+9Rr169Rg8eHCRj8Pe4MGDmTt3LsOGDSMuLo6IiAjWrl3LnDlzGDZsmCoQVNQ2nD17lurVqzNw4EBbQCJXZmYmS5YsoXXr1jc0b0lZJUEKIYQQJUNRrFcDLl+2puhouHIF4uJIj4/mytWLRKdd4UpmHNHmJKJJ5YpRIdoToj3hyrW/GXrA7VoqA4wWHf4WN/w07vjpPPHT+eCpCcaNCugpj4umHDqCsA4ZEYBi8sGcacSU6U5OhsE6xFGantRUre3O0NTU6z0T0tKsT92/kdF4/WJT3uThcf2x0Whdzr0bNzdQkDfZ5+UGHeTGTiGEEPZ0uuvDW127ibTITKbrI0ImJlqDF3mX7VPeESSTkkrm+zw1RUdqihFOG7EGNarnW9bLx4SPfyZGvxQMvvFo3C9gcj1NpuEkyYbjJHqcxOxzFgxF716iaOx7ZCjA1WvpCGStgePgfhjCrkJYkoaqOZ5UdQmkqlcVwoMjqFH1LozhEdaeGEFBZSuyL4QQZUSHDh3466+/ePvtt3nxxRdJTEwkICCAiIgI+vTpoyo7YcIENm3aZFveuHEjGzduBGDDhg20b98egKCgILZs2cLo0aMZOHAgJpOJVq1asXHjRmrXrm3b3tPTk507d/L222/z6aefcuHCBfz9/WncuDGffPKJKvDwyy+/ANZ5LFavXu1wHEoxvvwiIiKIiopi8uTJjBs3jpiYGHx9fQkPD3cY/ghg6tSp7Nq1i8GDB5OcnEzz5s355ptvqF79+nfjCy+8QKVKlZg1axZPPfUUGRkZhIWFMWHCBF566SVVfe3bt+e3335j/Pjx9OzZE6PRSI8ePZg+fToGw40PqaDX6/njjz8YO3YsU6dOJSEhgapVqzJt2jRGjRp1Q21QFAWz2YzZyVAGP/zwA4mJif+aCbNzaZTivJuEg7p162Iymfjnn39sY4wJkZfFYiEmJncYDnmPCEdl/j1isUBs7PXgw7VkuXyJuNizRF+9wOW0aKKzErjslsPla8GGy17Xgg8e1nGUS4tO0eCrGPDRuOGj8cLDHIxRqYibpQIGgnFRgnGhAhpLEOT4YcnwJCfdg+x0NzJSDaQka/POk01y8r8rqODmdj1IkPdvbsoNKhSW5+EBXl7q4ENZfDv/G5X5zxBR6urWrQvAoUOHSrklwhk5n/h3sFiu99hISLDep5F7v0Z0tPpxTIy13O3i7WsioEIKnuXicPE+h8V4lHS3QyS6HSLO4yh4xtzUpODOVEyG8AQIT9ZTQxtIuG81wqs0okbdNrjXb2INYNiNHy5unPwWEAW5E94fGRkZHD9+nPDwcNzd3Uu7Of85uRNna7Va29BKpWnjxo106NDBNrF3WZDbprVr1xIZGWkbOqusyg1yfPXVVwwZMoRdu3Zx1113OS1b2P/f7TqXKNvPqBBCiFsrIwMuXlQly8ULxEaf4mLiWS6mXeZiTgKXPCyq4MNlT7gSAOYSGoO6KAyKjiCM+Gu88DGF4mmqhsEcit4cCqZKKNkVUHJ8yUk3kp3uTkaqgYxUPSnJ1uGQYpLg1C0cp/p2MBqtgQAvL/D2Vv91lpcbTLAPQOT+dXeX6wNCCCFESdBqwdfXmuzm/XQqO9s61FRMjDV4ceWK9XFuQCO3U2p0tDXocTOSr7qQfNUP/vHDOtyUeqxxnU7BLygb78AU3Pzi0HqcQ3E7TqbbEVI8DpPgcxST5yXQFv0ujUve1rSJHOCyNSnb4OAcKm+D8Ktawi1+hHuFUqtcBLWrNadqvTa41Kxt/YEihBBClEGdO3cGKPCif1nw008/0atXr9JuRrFIkEIIIf6tcnLgwgU4dw7OnoWzZ0k/d5ILsSe5mHyBSxkxXNSlc9ELLnrDRS+45AWXvSHnNszpoENLOa0X5ZQK+CrV8VZq4KGpgSuhaLIqYErxIyvFm/SrHiQnGkiI0xIbq+Fg/J0xBr/BcD14YJ/shzwqeDgkCxkZsVStGoSra9m880kIIYQQxePqChUrWlNhMjIsHD4ch8USSGys1hbUyE25QY3Ll609P4vLbNYQF20gLtqAdfjK2sA9qjJ6vUL5ijn4l7uKh/cFtB7HyHE/SLLH38R4/02Cz1ko4s23F3zggo+FDcQD8WCJghOL0R+FGglQO92d2try1PatQYOQu4io3wnX+o0gMLD4ByeEEOKWs1gsWAo5SS/rPQ8K07RpU3bt2mVbjoiIKMXWFK59+/Z3VHtBghRCCHHnSk21Bh+uBSEsZ88Qc+Eo5+JPcTb1AufMiZzzVjjngy3FVQYq38I2KeCbXQX/7Dp459TBaKmBa05ldJnlIcMfU6oPGcmepCW5kZSoIy5Ow+XMW9ieG+DhcX0s64JS3h4MeYMPuY/1+pJpj8UCMTGKzL8ghBBC/EcZDFCpkoVy5QofSjE9/XrQ4uLF6/eqnDlz/W9KSvHbkJOj4cJZVy6cLQeUA5qo1ru5KVQMySKgUizuPscxG/eR7P4X0T5/Eet1ukgBjBwd/BME/5ABnLGmxLW4rp9GvW+gSaIbjfVVaBLUgAY122Js0BTq17f+MBNCCFFqJk2axFtvvVVgmdOnTxMWFlak+tq3b1+suS5uBy8vrzLdc8Ker6/vHdVekCCFEEKUXRaL9ezy1Ck4eRLl1EmunD3MibhjnEg9x2mXVM76Xg9AnPeG7BpAjZJvis6io5ypBn45tfEy18Etpyba9CooKeXJTAwgLcGLxFh3Yq/ouJql4WrJN6FQGo01cJA3iOAssFBQnre3TMYshBBCiDuX0QjVqlmTM4pinez73DmHET+5cOH63+LOmZGZqeHUMTdOHasCVAE62tZ5eSlUCsvALzgGvfEfTIYoktx2c8k3ikS/c4UGMLJdIKoiRFXMBI4Dx9Fe+Z5q30K9j6Butjd1vWpQr0oTataNxND4LggPlzEthRDiNnnmmWfo0aNHgWUqFqXroPhPk0sxQghRmnJy4J9/MERFQUICllMnuXDhMCcTTnIi6xInvM2c8IcT/nDSH9JKOghh1uGbUY2A7Dp4m+rgbq6FS2YoSkp5sq4GkJboxdVYN2KuaLmco+FyCe66MC4u1l79QUFFS/7+ci4qhBBCCFEQjQb8/KypYcP8y6WkwPnz13ti5Bk9lNOn4dIla8CjKFJSNBw5YIQDYUAY0M22zmhUqBiShl/AWfSeh8ky7ibeazcXgqMwGa/mW6dFCycCrOlHkoEoIArdkXmEb4N6CTrq6StRr3x96tWOpHrLbrjUjii8K4oQQohiq1ixogQhxE2TIIUQQtwumZlw8CBERZEVtYtDJ7azP+kYBwJMtkDEKT/IKqnJqC0afNKrEZTeBK/sRrhm1sNyNYzsxEDSErxJjHMjIU7HVeXW93xwdYWAAGsKDCzaY19f64m0EEIIIYS4vby8ICLCmpzJyroesMhNJ07AsWNw/Lh1fVGkp2s4ccQTqHstPWJb5x9oIqhCDK7eh8nw3MNln79IK3cQ/E6BzuS0PrMWjgTBkSAz33EOOAfnf8Vwegy1E7Q0NAfS3L8+LSLuoUG7R3ANKcIs50IIIYS45SRIIYQQt0J6OuzfD1FRxOzbxv6zf7Ev4zT7y1nYXx6OlANThZvbhVbRUjGrDgFZTfDKaYxLegTmxCqkxQYRf8mbyxdcScrWcAPzJxZZYKB1wscKFazJ/nFwsLWMp6cEHIQQQggh/i0MBqhZ05rsWSzWXhjHjl1PJ05Y0+nT1o7ERZEQ50JCXEWgItDZlq9zseBbPha3wMNk+f9JQvkdWMrvB9/8J+/OcsH6O5wYvmIdnFyH4eirNI53pYUuhBYVm9Gi6QNUvbsHGg+PYj8fQgghhLg5EqQQQoiblZICe/di2rOLYwc3sf/SXvabL7CvPOwvD9E3MVm1G3rC9FUpn9Mej4y7UeLrkHapClfO+HHutCsXMjRcKNGDsfaCL1fuerAhb8obhAgOtvaQEEIIIYQQIpdWC6Gh1tSli3qd2WwNYOQGLY4fvx7IOHnSur4wZpOW+Ivl4WJ5oIMt390jm4BKZ9H77yXVfyuxFXdA+YOgz3RaT5YL7CyfzU5OQPYJ2LGUoLXQMtmbFp61aFk9kmatH8G7/l0yTJQQQghxi0mQQgghiiM5GfbuJXP3Tg4e2sDeK/uI0l5hb7A1IJEZAoQUr0p3jSvV3atQ1asFPplt0cbXJTM6jLhzgZw+7sbRMxqOFHHM38JUrAhVqqiDDXmDDhUqWAMUMreDEEIIIYQoaTodhIVZU+fO6nU5OdaeFkePWoMWR49eT1euFF53RporF46FA+FAHwC0WoXgSnH4BB7A4ruDS+W3kFJlN3jEO60j1gN+8UjmF3ZBzC40K96n7jwtrXMq0Kb8XbRt9AChbXuiCQy8madBCCGEEHYkSCGEEPlJSoKoKFJ2b2P/PxvZG/M3US6x7K0Ah4LAFAqEFq/KSi7+1PVuTrDSE7fE5qSdr8754z4cOqRwMP7mIwPBwddP/KpWVT+uUgXc3G56F0IIIYQQQpQ4vT7/IaSuXlUHLg4dgr//hlOnCq7TYtFw6XwQl853BDra8gMCkvAp9zfZ/luJrrQdU+Uo8LrkMFyUooGDgRYOcpHPuAh//0TlLdA20ZM2HhG0rdmZum16o23QEFzk8ooQQghxo+RbVAghwNq3/OBB0rdu4K+9K9kVs5co1wSiKsDxAFCKGZDQK1pqG0Ko7nofPln3YLlSn4QzlThyyMDvJ2+8mVotVKsGtWtbU7Vq14MRoaHg7n7jdQshhBBCCFEW+fpC8+bWlFdKChw8aA1Y/P23dUq4v/+25hckPt6H+Pi2QFtbnpdXMl7l95ERtJ3E0B1QYQ94X3QIXFzwgaU+qSzlL0j8i8BvptJhuo6O2up0qtqJGi3vQ9OqFQQElMixCyGEEP8FEqQQQvw3pabCn39yaesqth39g+2pR9gWnM3eYDBVAaoUvSovxZV6uqZUVB7EPb0d6RfCuXDcj4MHtRxIv7HmublBrVpQp4411a5t/RseLr0hhBBCCCGEAPDyglatrCmXxWIdNmrfvutp7164eLHgulJSvElJaQcn2sEOa56HZyJu5aJIqrQNU5WdUOkvh6Gi4jxgeW0zyzkGHKPy+k/oOB86ZlagS2gHKra9DyIjofINTlInhLjjLVu2jEmTJnHq1CkyMzPZu3cvjRo1KvL248aNY9++fezdu5dLly4xcOBAFixY4LTsqVOnGD16NOvXr8dkMtGqVSveffddmjRpoiqXnJzMu+++y/Lly7lw4QJ+fn60bt2aiRMnUrdu3Zs42pKh0Wic5r/zzju89tprqrw1a9YwadIkoqKicHV1pV27dkybNq1IxxEWFsbZs2cBGD58OB999JFq/dmzZ3nrrbdYvXo18fHxBAYG0rx5c1asWKEqFxMTw5gxY1i5ciXp6ek0bNiQKVOm0KlTp0LbMG/ePFauXMm+ffu4cuUKlSpVokuXLowfP54KFSqoyvr6+pKUlJRve+9kEqQQQvw3XLiAeesWDv35C9vObWWb5gLbKiuc8QNqFb2aAIsntbO6EGDqgS65JVfPhXLisJEd551/gRbG0xPq1VOoVi2Dxo3diIjQUqcOhITIvBBCCCGEEEIUl1YL1atb00MPXc+PjbUGLKKirEGLqCjrxN0FSUv1Iy21E5y6fpHJw+8cporbyaqyAyr9CRWiwCXHtv6CD3zVCL7iMrCE+tuWcM9X0DUtmLY1u+AW2Qk6dLD+4BdC/OvFxsYyYMAA7r33Xj7++GMMBgM1nY1rV4BZs2bRoEED7r//fr744osC99W2bVv8/Pz44osvcHNz45133qF9+/bs2rWLWrWuX/zo2bMnu3fvZuLEiTRt2pTz588zZcoUWrVqxYEDBwgNLebY1rfAww8/zMsvv6zKC7H77Pzpp5/o1asXDzzwAN9//z1JSUm89dZbtG3bll27dlG9evVC93Pffffx5ptvEhwcrMo/ePAg7du3p1q1arz//vtUrlyZy5cvs2bNGlW5rKwsOnXqxNWrV/nggw8oV64cc+bM4d5772Xt2rVERkYWuP8JEybQoUMHpk6dSqVKlTh69CiTJ0/mp59+Yu/evZQvX95Wdu3atbbg07+NBCmEEP8+igKnTpG5bg3b//qOrbFRbPNOYmdlSPYFfItWTcX0UMLS7sM7+x5MCY2JOVWBY//o2ZZZ/ICERmPtBVG/PjRocD2FhQEoxMQkU66cG1ptsasWQgghhBBCFCIoCLp0saZcycnWIaKiomDPHmv65x/r6UR+0hJDIDEEDvUDQKfPQFtlGzlh66HqBqi4G3QmW/kD5a1pBtG45Swicu0iun4KXbXh1OnUF80DD0KTJtYTBiHEv86xY8fIycmhf//+hV6szk9KSgraaxcLFi1alG+56dOnExsby/bt221BhjZt2lC9enXGjx/PsmXLADhx4gSbN29m3LhxvPLKKyiKgsViITw8nLvvvpsffviBl1566YbaWpLKly9Py5YtCyzz6quvUr9+fX744Qdb74vWrVtTs2ZNxo8fz9dff13ofoKCghz2oygKAwYMoEqVKmzZsgWDwWBb17dvX1XZ+fPnc/DgQbZv324LHnTo0IGGDRsyZswY/vzzzwL3v3fvXsqVK2dbjoyMpEmTJjRr1ozPP/+ccePG2dbdddddhR7PnUqCFEKIf4fLl2H9ek5v+olV59bxW0AC66tCRhGHbnLJNlIzrjuBqQ+TeaUNF46U59JFHZduoCn+/tCwoTogUbcuGI3Oy1ssN7ATIYQQQgghxE3x9oa2ba0pV2qqtcfFnj2we7c1HTmSfx3mHHfMpzrDqc4A6FzTocpWzFXXQdX11p4WWusP/kw9rKlhTXCcKklT6PrWFLom+tOp4YP43d8X2rcHV9dbdchCiNto0KBBLFy4ELBe2O7bty+RkZFs3LgRgN27dzNp0iS2bt1Keno6derU4fXXX6dPnz6qerRFvJtxxYoVdOzYUdULwtvbm969e7No0SJMJhMuLi7o9XoAfHx8VNv7+voC4FbMMaazs7N57733WLx4MadPn8bb25sePXrw3nvvERQUZCsXFhZGvXr1GDJkCBMmTODYsWNUrFiRF198kREjRhRrnwDx8fEcPXqUV199VTU8VGhoKPXq1ePHH3/EbDaju4FhKjZv3sy+fftYsGCBKkDhzIoVK6hVq5aqd4OLiwv9+/dn7NixXLx4kUqVKuW7fd4ARa6mTZui0+k4f/58sdt+p5IghRDizpSYCJs2kbXud7Yc/JXfDOdYVQOOVALy/+y38U2qQfX4h3FL6UnCqXqcOOzF4Zzi3b2k01nnimjY0BqIyA1MVKwoN0IJIYQQQghxJ/L0hDZtrCnX1avWYMWff15PMTHOtzdnG+HkPdYE6NySMYeuh2rroOo6CPrHNhn3eR+Y1xTmkYDW8gUtvv2Cbu8beCi4AxHdnoBu3ayzhgvxH5SUBAcOlHYrHNWvD3bX9vP15ptv0rx5c4YPH87UqVPp0KED3t7eAGzYsIF7772XFi1a8Omnn+Lj48M333xD3759SU9PZ9CgQcVqV0ZGBidPnqRXr14O6xo0aEBGRganTp2iZs2ahIaG8sADDzBr1iyaNm3KXXfdxblz53jxxRcJCQmhX79+Rd6vxWLhgQceYMuWLYwZM4bWrVtz9uxZJkyYQPv27dm9ezfu7u628vv27ePFF19k4sSJBAcH8/XXXzNy5Eiys7MZPXq0qu4lS5Ywf/58LBYL9erV4/nnn2fw4MG29dnZ2QBOgwgGg4H09HROnjxZ7OG1wBqkAPDy8uK+++5j/fr1uLi40L59e95//31q165tK3vw4EHa5o12X9OgQQMADh06VGCQwplNmzZhNpvLxPwgt4sEKYQQd4b0dNi6Fdav5+z231iVeZBV1RXWVYO09oVsa3Yh5EoHKiQ9jCW+Ixf+CeXyRT17irH7wEB1MKJBA4iIgEIC6kIIIYQQQog7nK8vdO5sTWAdDurcOdi2DTZsgI0b4cQJ59uaM73h6IPWBGi9rmAJW2vtZRG2AfxOgwYsWthRBXZUyWI8q6mzbTUPz9fwsEdz6nd+HM0DD8g8FuI/5cABdS+nsmLLFnUQsyDVq1cnIiICgPDwcNWQQsOGDaNu3bq2i98AXbt2JS4ujrFjx/LEE08UuQcFQGJiIoqi4O/v77AuNy8+Pt6Wt3z5coYPH07Hjh1teQ0aNGDTpk34+fkVeb/ffvstq1ev5vvvv6d37962/IYNG9KsWTMWLFjA0KFDbfmXLl1i7969NGzYEIBu3boRExPD5MmTGTZsGMZrQ1A89thjdO/enSpVqhATE8P8+fN58sknOXXqFJMnTwasw0H5+/uzbds2VZuuXr3KwYMHHY65OC5evAjA4MGDeeSRR/j111+5fPky48aNo23btvz999+2Sa3j4+OL/LwXRUpKCsOGDaNKlSo8+eSTN9T+O5EEKYQQZdelS7ByJTm//MjWo2v5LSyH38LhcKdCtjPr8LvUjtC4IWRd6MKZw0Gcy9Bwroi7DQ2Fli2tQ8PmBiSCg6V3hBBCCCGEEMJ6XhAaak2PPWbNO38eNm2yBi02bIDTp51va0kpDwcetyYAn7PWYEXVa3Na+FwA4J8gmBykMJk/CT/+Jw8/OYKHzbVoHNkXzf0PQOPGcoIixB3qxIkTHDlyhPfffx8Ak+n6PDb33XcfK1eu5OjRo9SpU6fYdWsK+FzIu27o0KGsWLGCWbNm0bhxYy5fvsyMGTPo2LEjGzZsKPLE2StXrsTX15eePXuqjqNRo0YEBwezceNGVZCibt26tgBFrscee4w//viDqKgo2lyLANnPJfHQQw/Rs2dPpk2bxogRIwgKCkKr1TJ8+HAmT57M5MmTefbZZ0lOTubFF18kPT0dKPpQWfYs18blbtWqFfPmzbPl16tXj8aNGzNnzhymTJliyy/q816YzMxMevfuzdmzZ1m/fj2enp430Po7kwQphBBlh6JYb5f4+WdSfl3BmuQofqoFv9aFxMLmBroaQrVLg3GPfoRz+2uRmORCYhF2qddD06bQurU1tWplHa5JCCGEEEIIIYqqShXo39+aAM6cgfXrYd06a7pyJZ8Nk0Jh/yBrAvA/BuGrIPw3CN0E+iyOB8A7beEdjlI1cRIPvTaJh+KCaN7qYbT3PwDt2t36AxRClJgr1z4QRo8e7TDEUa64uLhi1enn54dGo3F6135CQgJw/c7+1atXM3/+fJYvX87DDz9smzi7a9euVK1alYkTJ/Lll18W+ViuXr2Kaz5z6dgfR3BwsEOZ3LzCehz079+flStXsnv3brp16wbA+PHjSU1NZcqUKYwfPx6A7t27M3jwYObNm1fsYZZyBQQEANbeLXk1atSIChUqEBUVpSpblOe9MFlZWfTq1YutW7eycuVKWrRocUNtv1NJkEIIUbqys2HzZvj5Z6L/WMHPHhf4qRas7QzZBX1CZXngfeE+KsQ+SfKR1lw+482pIuyufPnrAYnWra29JYo5J5QQQgghhBBCFCgsDJ580poUBQ4fvh602LjROt6+Uwk14c+a8OdIcEm39rAI/80auPA7w2k/eP9ueJ9YKiV/Qu8PPqH3S+7cFd4ZHn0MuncHL6/beKRC3Br161uHVipr6te/+ToCAwMBeP3111VDJOVVq1atYtXp7u5OjRo1OOBkIo8DBw7g7u5OtWrVAOu8EADNmjVTlfP19aVGjRq2oZKKIjAwkICAAFavXu10vZfd51F0dLRDmdy83MBAfhRFAdS9I1xcXJg5cyaTJk3i9OnTBAYGUqFCBVvApXLlykU+lrxy55PIrx1521C/fv18n3ew9r4oTFZWFg8++CAbNmzgp59+olOnwoYQ+feRIIUQ4vZLTIRVq+DnnzmycyU/Vknjp1qws6C5mSwaiG5M5ZhBuJzpwYVDoSTnaEkuZFd16kCHDteDEmFh0itaCCGEEEIIcftoNFC3rjW98AKYzbBvn3VYqPXrrRdiU1OdbGgywvEe1gQQdBAivrOmcoe46A0ftoAPW2RQPvUXei/+hT7j9bSt3RXdQw9Dz55QxDt4hShrfHyKPvfDnaZWrVqEh4ezf/9+pk6dWmL19urVi9mzZ3P+/HmqVKkCWOc3+OGHH7j//vttc19UvDZ8xM6dO1XDOsXHx3Ps2LFiXSDv0aMH33zzDWazuUh3/h86dIj9+/erhnxasmQJXl5eNGnSpMBtFy1ahF6vp2nTpg7rPD09qX8tghQVFcW6deuYMWNGkY/DXrdu3TAajaxatYqXXnrJlh8VFUV0dLRqfpFevXoxbNgw/vzzT9tzYDKZWLx4MS1atLA93/nJ7UGxfv16fvjhB4feG/8VEqQQQtweKSmwYgXmxYv489h6fqxp4adacGxwAduY9OjPdqXihedJ/LstyfFGLhSyG39/64R2XbtCly7WbtdCCCGEEEIIUVbodNYhZ5s2hdGjIScH9uyx9rJYvRq2b4drw6GrxdaDTfVg00QI/Od6wKL831zxhE+awSfNciifupLey1fyyGQt7ap1RNenLzz8sHUGcCFEmTB37ly6detG165dGTRoEJUqVSIhIYF//vmHqKgoli9fbiu7adMmYmNjATCbzZw9e5bvvvsOgMjISIKCggDr8FGLFi2ie/fuTJo0CYPBwLRp08jMzGTixIm2+nr37s348eMZOnQoFy5coHHjxly6dIkZM2aQnp7OyJEji3wc/fr14+uvv+a+++5j5MiRNG/eHL1ez4ULF9iwYQMPPPAAvXr9n727jq7iWsM4/JskJCQ4FHd3LW7B3dtSHEopUChtoZRbaAuUCu4ttBR3d9fgXgiuxaXBIUggOXP/OOGQQLCQZCLvs9ZZl/lmTvJyuzkw+WbvXd9xfapUqahTpw69e/cmZcqUTJ06lTVr1tC/f3/HptkDBw7kyJEjVKxYkTRp0jg2zl69ejW9e/d2zEQB8PLyYvfu3eTLlw/TNNm1axf9+/enWrVqfPHFF2//HyZQwoQJ6dOnD127dqVVq1Y0btyYq1ev8uOPP5IuXTo6dOjguLZ169b88ccffPTRR/Tr149kyZIxatQojh8/ztq1a4N93YoVK7Jx48Zg+3d8+OGHrFixgu+//54kSZKwY8cOx7n48eM7Nl+P7tSkEJHw8+QJrFmDOXUK+7ctYFp2P2bkhculXvEevzjEPfsByS615+o/hXngG4tzr7jcxcW+j0SVKvbGRKFC9n/0i4iIiIiIRAWxYkHx4vbX99/bJ56vWQPLl9snoPv4hPCm6zlh04/2V+ITkGse5FgAqXcHaVjYSOa7lobz19L8l88p8n5tjKbNoEYNrXkrYrHy5cuza9cufv31V77++mtu3bpFkiRJyJUrFw0bNgx2ba9evdi4caPj2MvLCy8vLwA2bNhAuXLlAEiaNCmbN2+ma9eutGzZEn9/f0qUKIGXlxc5cuRwvD9u3Ljs2LGDX3/9lT///JOLFy+SOHFiChYsyOjRo4PNEngdZ2dnFi9ezPDhw5kyZQp9+/bFxcWFNGnS4Onp6Zjd8FSBAgX45JNP6NWrFydPniRVqlQMGTIk2GyFHDlysHjxYpYtW8atW7dwd3enQIECzJgxg0aNgi/B4erqyrx58/jll1/w8/Mja9as9OnThy+//BLnd/zh0DfffEOCBAkYPnw4M2bMIF68eFSrVo1+/foF22fCzc2NdevW0a1bNzp16sSDBw8oUKAAK1aswNPTM9jXDAgIICAgIFht6dKlAPz666/8+uuvwc55eno6/ltHd4b5dEEvCZXcuXPj7+/P0aNHQ71jvERvNpsNHx8fkiVLFjPGiGnCrl0wdSpnl09neuqbTM0HR5O+4j0PEpH0Qmvin23FhX9y8tjv1X+RZMlib0pUqWJfyil+/LD9LUS0GDdG5K1ofMjraIzI6+TOnRuwT6+XyEf3E/I6+pyP2Ww22LcPFi+GefPgtR/l8S9AjoWQcz6k2wzOz34Ylv06NPeGZmfikb5qQ2jaFDw9QeMqWosKnyEPHz7k5MmTZM2aFXd3d6vjxDhPN852cnLCCMf1sTNkyECePHkcP5SPDDJkyICnpyfjxo3Dyckp0v4ZeSogIADTNIkVKxYdO3bk999/f+ev+bo/fxF1L6GZFCISNk6dgmnTuDF7ErM9zjAtH2xt8Yrr76Yk/ZWvcDnRkLP7M3AtwODaSy51cbE3I+rVg2rVIHCvJxERERERkWjNyenZ0lA//QRHj8LcufbXgQMhvOFuWtjVyf5yvw7ZF0Pu2ZBpHcff8+eHivAD9yh7dhzNu47jowcZSPBpR/jkE3jNprUiItHR5MmTmTx5cpj90D88JUmShDt37lgdI1yoSSEioXftGsyezYPpk1hyZzfT8sGKD8D/ZRMhHiQi/dnvMY404eyhlK9cxsnd3b58U4MGUKsWJEoUHr8BERERERGRqCNnTvjxR/vrxAl7s2LePJN//gnh6eeH78H+1vaXhw/kngN5p0Oa7WzKYLIpA3R6cpZ6O76lxfjuVC78MS6fd7SvOxWOT1OLSNQQdN+EkESFmQevs2TJEvz8/ABIliyZxWlez8vLy/HfJSrkfRtqUojI29u7F9uggazbO4dpuW3M94R7bi+51oQ0txqS+GgPjm3Kyzm/l/8FliCBvSHRoIG9QREnTvjEFxERERERieqyZYMePeC770z27r3Oli1JWLjQic2b7avwBvMgGezuaH8lOAd5ZkDe6TxKcZCZeWFmXn9S3JtG097TaPEwG/madoHGjaP+2roiEipnz54lY8aMr7ymV69ewTbkfpOvGdk8v2dGZFegQAGrI4QbNSlE5M3YbLBiBX6DBzD19iYGlIITTV9++Xu2TOT6bwAXNtTgzAl3Lr7kumTJ7Ms4NWhgX9LJ1TU8wouIiIiIiERfadMG8NVX0LmzfaPtxYth/nz7BtwvPAx9Jz1s/c7+SrEPCo6HvNO5Gu8mg0vCYE6Qf397Pp/YiaZZGxD3k3bau0IkhkmVKhW7d+9+7TUiYUVNChF5tUePYOpU7o0YyJi4JxhSAi6/5GEaDyd3yjh/R8CeT9myIhWbHoU8RThVKvj4Y3tjokQJcH71PtkiIiIiIiLyhpIlgzZt7K8bN+wbbs+YARs3hjDD4mpBWDESVg+yb7hdcDxkWot3Chvtqz3hf49m8Un/WXT4KjVZP/gMWraEDBks+F2JSERydXWlcOHCVseQGERNChEJ2Y0bMHo0PmOHMyLzdf6oAbfdX7zMGScqpGhAqnP/Y/fiQqw6HPLTNYZh3/S6XTuoWdO+GbaIiIiIiIiEnyRJoG1b++viRZg1C6ZPh3/+ee7CADc4/LH9Ff8CFJgIBcdzJ9FZhpWAYVyi6qnefFG1N9XTVsC5XXv7lPhYsSz4XYmISHSjHxOKSHCnT8PQoZyZN5bBhfwY1wwehfDvzgQu8amfYAAPd7RgcX93Hj4M+culTAmffmp/6YEbERERERERa6RJA998Y38dOwaTJtlfV648d+HdtLDpR9j0PWReA++PgeyLWZXFn1VZINPN9XQZuJ5PuiTHo2UbewckXTpLfk8iIhI9RIsFBU3TZP78+ZQvX56UKVPi4eFB9uzZadeuHf/+++8L19+9e5cuXbqQPn163NzcSJ8+PV26dOHu3bsWpBeJJLZvhw8/5ECpLDS78gdZ2/rxR9EXGxQp3dLQxFxKpvk3mdipHbOmvdigeDprYv58OHcOfv5ZDQoRERGJvHQ/ISIxTY4c0LcvnD8PS5fal+J9cba7E5yuCrPnwZALsKYv3MjMv4nhi5qQvvF/9NnyKzdyZYDatWH5cggIsOB3I9GFzWazOoJIjBNZ/txFiyZF165d+eCDDzh+/Dj16tWjU6dOZMyYkb///psCBQpw6NAhx7X379/H09OToUOHkj17djp37kyuXLkYOnQonp6e3L9/38LfiYgFtmzBLF2KzY1KUtNtHvk/h2n5IOC5T4fMboX48OYunH4/z/SfarLvnxc3kkiRAnr0sE/GWLEC6tfX7F8RERGJ/HQ/ISIxlYuLfTneefPg8mUYMgRy5w7hwvsp7BttjzwFk9bAsdpcd3eiV3lI95XJlwFLOdu0JmTNCsOHw717Ef57kajL1dUVQH+Hiljg6Z+7p38OrRLll3u6evUqw4YNI0OGDHh7exM//rMdfYcNG0bnzp0ZMmQI48ePB2DAgAHs37+fbt260b9/f8e1vXr1ok+fPgwYMICffvopwn8fIhHu3Dno1o3t22bzbRXYWjnky/I41yPt0UFsWpSJufdD3gi7ShX7XhO1a6spISIiIlGL7idEROySJoXOneHrr2HHDvj7b5g5kxeX9j1Tyf5KdAqKjeRBgQmMLHaPUUWg4eEz/K/v1+Tv2RM++wy+/FJLQclrOTs7kzhxYq5evQpAnDhxcHKKFs9VRwmmaWKz2XBycsIwQv65j0Q/NpuN+/fvc/XqVRInToyz84sPI0ckwzRN09IE72jHjh2UKFGCpk2bMnXq1GDnTp48SbZs2ahZsyZLly7FNE3SpEnD3bt3uXr1KnHixHFc++jRI1KlSoWHhwcXLlx44z+UuXPnxt/fn6NHj+oDVEJks9nw8fEhWbJkkWOM+PpC//5c/nMg/yvjx9T8IVxjQmH/zrjt7s62Ne9hmi/+eYgb177PRKdOkDlz+MeOziLdGJFIReNDXkdjRF4nd+AjsYcPH7Y4SeSk+wmJ7PQ5L68TnmPkzh2YNg3GjAFv75dc5HoXCo6HYiMhsX2JvOon4X9boOxFJ4wPP4IuXaBo0TDNJm8mqnyGmKbJpUuXuHnzptVRYhzTNDFNE8Mw1KSIgRInTkzq1Klf+t8+ou4lovxMiqxZs+Lq6srWrVu5d+8e8eLFc5xbvnw5ABUqVADsNxmXL1+matWqwW4oAGLHjk3ZsmVZtGgRp06dImvWrBH3mxCJCDYbTJmC3/ffMSzDVX5uC/efn8kVEIvidwdy16sNe7zjhPhl0qaFr76CNm0gQYLwjy0iIiISnnQ/ISLycgkSQIcO8PnnsGcPjB4N06eDn1+Qix7Hh51fw84vIfsSKDGEFVk2sSIrFL9g439bZ1Gn+CycSpWG776DGjXsGxmKBGEYBmnSpCFlypQ8fvzY6jgxis1m4+bNmyROnDhSN7Ik7Lm6ulo+g+KpKN+kSJIkCb/++ivffvstOXPmpE6dOsSLF4+DBw+ydu1a2rZtS6dOnQD7TQXw0huGp/WTJ0/qpkKil61bMb/+iqV399KlAZxK8tz5hwkpcHkUV9d9yI7LIa/XVLSo/eGXDz4IaUM1ERERkahJ9xMiIq9nGFCkiP3Vvz/89ReMGgVXrgS9ygmO17W/Uu6FEkPYkWsO9Rs9Icc1+N/WLTSpWwvX3PnszYqPPtLNpbzA2dkZd3d3q2PEKDabjVixYuHu7q4mhVgmWvxt0LVrV1KlSkW7du0YPXq0o16yZEmaNWtGrMBF8u/cuQNAgpc8/v10/dmn14XEz88PvyCPDNhsNsfabSIhsXSMnDuH8d13nFg3m6+rwcrn75X9XUl9eBA3V3/O/vsvfhw4OZnUqwedO5uUKPHsYRcN97ClzxF5FY0PeR2NEZF3p/sJicw0RuR1InqMJEkCPXpA164wZw6MHGmwe/dzMyOuvA/zp8Ga/lD0d469P4ZP6t2iZ3n4ZtsB2rRsgscPP2B27QotW0Ls2BGSPSbSZ4i8jsaIRAbRoknxyy+/0KdPH3r37k2LFi1IlCgR+/fvp0uXLpQvX57Zs2fToEGDMPleffv2fWEjvESJEuHj46Nuo4TIZrNx584dTNOMsDFi3L9PnN9/J2D8aH4u7sfwDuD/3OyteGca4rZuNJcuJn7h/XHi2Gjc+CFt2jwgffoAAK5di4jkMZMVY0SiDo0PeR2NEXkdf39/XPSk6ivpfkIiM33Oy+tYOUYqV4ZKlWDv3liMGePBsmWxsdmCNCzupYF1/WDTj1BwPBdKDuLr6uf52RO+2vEvHb/pQIKffuJ+x448aNkSXJ9fk1jelT5D5HU0RuRVIupeIsrfraxfv54ff/yRzp0706NHD0e9VKlSLF26lEyZMtG5c2caNGjgeOLpZU823b17F3j5k1EA3bt3p0uXLo7jYsWKERAQEOk3IBLr2Gw2DMMgadKk4T9GbDaYOhXz+x5MTnaF7m3hv7jBL3G+lY10O+ZxZmce7j339rRpTb780uTTTyFBAndAUywjQoSOEYlyND7kdTRG5HXUoHg13U9IZKfPeXmdyDBGatSwv86eNRk5EsaNg3v3gjQrnsSBXZ1gT3vIO40bpfvTs8IxBpSCdnv/45sBPUkxcSLmzz9Dw4agsR5mIsP4kMhNY0ReJaLuJaL8HcuyZcsAKF++/AvnkiZNSt68edm+fTvXr18PtkZsSF63xiyAm5sbbm5ujmMnJydsNhtOTk76gywvZRhG+I+REyegZUt2XthBp5qwO/Vz5x+7k/HQ31xa1ZgzfsFzvPce9O0LrVoZuLhoAzMrRMgYkShL40NeR2NEJPR0PyFRgT7n5XUiyxjJlAmGDoXeve2NiuHD4fz5IBfYYoF3K/BuATkX4Fu6L4NL7uWPItB+z7/8r11TUgwdCgMGQAifyxI6kWV8SOSlMSJWi/Ij7/HjxwBce8laNE/rbm5uZM2alVSpUrF161bu378f7LpHjx6xadMmUqVKRZYsWcI3tEhYMk346y/uFivAp8l2UPyz5xoUJiQ725H3JlznzOKmPA7SoHB2hk6d7P2NNm20Z5mIiIjEPLqfEBEJewkSQJcucPo0zJwJhQo9f4UTHP0A/t4Dk1fz6HJphpWATF/BN4n38F/tClCzJhw6ZEV8ERGJYFG+SVGqVCkAhgwZ8sK060mTJnHq1Cnef/994sWLh2EYtGnTBl9fX/r06RPs2r59+3Lr1i3atGmDYehJcoki/vsP6tRhd5/2FGrxkPHP/cMv9s33ybT4DD4Tf+f6FY9g5zw9Yd8+GDECEiWKwMwiIiIikYjuJ0REwo+LC3z8MezZA6tW2e9DX/BvZZiwGaYu5+G1ggwpCRm/hm+fLMenRD749FO4fDmio4uISAQyTNM0rQ7xLgICAqhUqRJeXl4kTZqUOnXqkChRIry9vVmzZg1ubm6sXbuW0qVLA3D//n1Kly7N/v37qVy5Mu+//z7e3t6sWLGCAgUKsGXLFuLEifPG3z937tz4+/tz9OhRTYmSENlsNnx8fMJ+neElS7B92prBWa/To+JzG2M/ik/2QzM5taIaAQHBb5JTp4bBg+3LfOr+OXIItzEi0YLGh7yOxoi8Tu7cuQE4fPiwxUkiJ91PSGSnz3l5nag2RrZtsy83vHTpSy7INQfK94Skx/B4DN9sh27/uBO38/+ga1d4i89YiXrjQyKexoi8SkTdS0T5kefs7MzKlSvp378/adOmZcaMGQwbNowjR47QpEkT9uzZ47ihAIgTJw5eXl507tyZY8eOMXjwYA4dOkTnzp3x8vJ6qxsKEUvcvw/t2nG1SR2qVbtOtypBGhQ2gyTHu5Jo7DWOL60erEHh6grdu8OxY/YnWdSgEBEREdH9hIhIRCtZEpYsgQMHoHHjEO5Nj3wEow7BwvE8eJCOnz0ha5uHjF/Um4BsWWD8eAgIsCS7iIiEjyg/k8JqevJJXidMO9K7dkGzZqw0T9KyHvjEDXLuelaSrFnCjePZX3hbjRowbBi8Yg9HsZCeWpBX0fiQ19EYkdfRTIrITfcT8jr6nJfXiepj5NAh+PFHWLgwhJNOj6HoH1D2Z/C4Rb6rMGQVVIyXDwYNgsqVIzpulBPVx4eEP40ReRXNpBCRZ/z9oU8fHpcuQdeMJ6neLEiDwmYQa09nXMceeaFBkTmz/QmVZcvUoBAREREREZHIJ08eWLAAduyASpWeO2lzhR2dYcRp2NaZA++5Uqkl1M59gGNNqtifyFMTXkQkylOTQiSyO3UKypTh5PBelPzExuCSQc7dSUPc2dt4snQIjx+5OMoeHvDLL/YnUmrVivjIIiIiIiIiIm+jWDFYswbWrbP/OphHiWD1EPj9KBz6iKXZIE8H6GxbwZ0ieaFdO/jvP0tyi4jIu1OTQiSyMk0YOxYKFGDywx0UbA97Uz09B3g3xXXMCXyPFQ/2tooV4cgR+P57iB07wlOLiIiIiIiIhFqFCrB9u335p2zZnjt5OxPMnQ1jtxNwsSTDSkC2jiYTd43BliUz/PorPHhgRWwREXkHalKIREbXr0P9+tz94jOaV7lPy/pw3zXw3P33cJ23CBZM5fF9d8db3N1h5EhYvRrSp7cmtoiIiIiIiMi7MgyoW9e+OsDvv8N77z13waXiMH4rLJiAj1MiPqkHpT6+z94/foDs2WHKFLDZrIguIiKhoCaFSGRz7BgUKcLu3Yso1A6m5g96rjax/jrO40N1gr2lWDHYvx+++AK0x5GIiIiIiIhEB7FiQceO9lWQv/sO3Nyeu8C7FfxxBI40YEdaKNIW2hW4yPX2LaBcOe1XISISRejHmSKRyebNmCVLMCzFWUp+CqcTB9YfxcNYNA5mLubJ3cSOy11c7HtPbNkSwjRYERERERERkWggQQLo2xeOH4dmzZ47eT8FzJ4Hs+Zg+iZnTGHI1gnG3t+MrUB+6N5dS0CJiERyalKIRBazZ/O4aiXalLlN52rg7xxYP+OJ819HMPe1DnZ57tywa5d97wkXlxe/nIiIiIiIiEh0kj69fSWnzZvtqzoFc/RD+6yKfS25FRs+qwOezQM4Mq6f/QZ6+XJLMouIyOu90Y82K1So8M7fyDAM1q1b985fRyTaMU0YMoRrPbvyQSPY/HQ/iSexYd2vsKMLAUEuNwzo2hX69NHG2CIiIiIiIhLzlC5tX/K4Tx8YMAACnt40P0oMiybCocZQqx1b0p+jQHv435azfF+3JrHrfgDDhkGaNNaFFxGRF7xRk8LLywvDMEL9TUzTfKf3i0RbAQHQuTOHZo2k9mdwNlFg/UoBmD8NruUKdnnGjDBpEpQpE+FJRURERERERCKN2LHht9/go4+gdWt708LhdFUYdRgq/MCTYiP4xdPGzDzw59J5VMy5Cvr1g88/16aOIiKRxFt9GpumGaqXiITgwQP46COWrhpJiU+DNCgONIZx215oULRtC97ealCIiIiIiIiIPFWwoH0p5N9+e25j7SdxYNVQGLsdrublVBKo1BJaVPblVtcvoGxZOHbMstwiIvLMW61kX7lyZUqWLPlW32Dbtm2sWbPmrd4jEu1du4ZZpzaDnXbSrTGYBmBzgnW/wdb/Bbs0RQoYNw5q1LAmqoiIiIiIiEhkFiuWfX/s+vXhs89gy5YgJy8XhTF7odQAKPszU/L7sS4jjF+0lar588OPP0K3buDqall+EZGY7q2aFFWrVqVLly5v9Q0GDx6sJoVIUKdO4VezGu1znmZiwcDawwQwbwacqh7s0jp1YPx4SJIk4mOKiIiIhJVNmzaFydcpW7ZsmHwdERGJnnLkgI0bYcwY+N//4O7dwBO2WLD5ezjyIdRuy+UMm6jWHNrvfszAn38k7uzZMHYsFC1qaX4RkZjqjZsUWrZJJAzs2IFPw5p8UOkmW55ukH0tO8xcBDeyB7u0Vy/o2VNLZIqIiEjUV65cuXfeo84wDPz9/cMokYiIRFdOTtC+PdSuDR07wqJFQU7eyA4TN0LREVDpO/4s8pDVmWHSwoOULl4cOncOYd0oEREJb2/04899+/axb98+WrRo8dbfoGXLluzbt49//vnnrd8rEq0sWsTBhuUo+kGQBsWJGjB2Z7AGRZw4MHcu9O6tBoWIiIhEL6F58El73YmISGikTg0LFtjvr1OkeO7kri/hz/1wvgT/Joayn0C3SiaPRgyBUqXg33+tiCwiEmO90Y9A8+fPT/78+Xnvvffe+hu89957jveLxFQe48ezpHsDSjbz41xCwAQ2/w9mLAG/BI7rMmSAbdvggw8sCioiIiISzoI2Hd7kJSIiElqGYb+/PnIE2rR57uTNbDBhM6zph+nvxsBSUOQz8L64174b97x5lmQWEYmJwuQ57Tt37tCtWzfef/99cuXKRcOGDdm1a1dYfGmRqM1mg27f8ufy76nfCHzdgMfuMHcGrOsH5rM/guXLw+7dkC+fdXFFREREwtOgQYOw2Wxv9Ro4cKDVsUVEJIpLlAj+/htWrrTPsHAwnWHr/+wba18uxKHkUPQzGJjnLgEffQhffAGPHlmWW0QkpnjnJoWfnx9lypRh8ODB7Nu3j2PHjjF37lzKlCnDxo0bwyKjSNRks+HXrg2fnB7C/yqDaQC308L4LXC4UbBLO3WCVasgFJOVREREREREROQNVK0Khw5By5bPnbiWG/7eCRt685hYdKsCFVvCual/QMmScOqUJXlFRGKKd25SzJw5k0OHDpEqVSq+/PJLunbtSu7cuXny5Ak9e/YMi4wiUY9p8uSLz/no/gQmFwisnSsNY/bA1UKOy2LFgrFjYcQI+69FREREoqM6depQp04dsmbN+tbvzZo1K3Xr1qVOnTrhkExERGKahAlh4kRYuBCSJQtywnSBjb3s9+2XC7ExA+T7HKYE7MMsVBBmzbIkr4hITODyphc+fPgQd3f3F+r//PMPhmGwefNmMmTIAMBPP/1E6tSp2bNnT5gFFYkyTJOArzrRwmcMS/IG1va0heW/g+1ZJyJ5cpg/3/5QhoiIiEh0tnDhwlC/92mDQ0REJCzVrWvfI7tjR5g9O8gJn3z2WRWlBnDXsw8tGvix5LAvf37SiMRr18KwYRAnjlWxRUSipTeeSZE7d25Wr179Qt3Z2Rmw70vx1MOHD3ny5InjnEiMYZqYXb+h/dk/mJkXCHCBpaNg6V/BGhSFC8OePWpQiIiIiDx18+ZNlixZwuzZszl8+LDVcUREJAZ47z37BImZM59bftl0gS094K9/4GJR5uSGvJ/D2vVj7Tf03t6WZRYRiY7euElx9uxZqlevTtOmTbl27ZqjXqpUKUzTpEyZMtSvX5+PP/6YXLly8eDBA0qXLh0uoUUiJdPE7P4d3xweytj3AZsTzJ8Kez4PdlmzZrBpE6RJY01MERERkcjm77//Jm3atNSrV4/GjRuTL18+6tatyyNtVioiIhHg44/h8GFo2PC5E9dzwbhtsGogl91jU7kFfJ3hGA9LFoGRI8E0LckrIhLdvHGTYsuWLeTKlYsZM2aQI0cOxo8fD0D9+vUpV64cvr6+LF68mLlz5+Lj40PcuHHp27dvuAUXiXR69aL3zgEMLQGYwLJRcPhjx2knJ5NBg2DyZAhh5TQRERGRGOnAgQN8/vnnPHz4ENM0Ha+lS5fyww8/WB1PRERiiGTJ7LMq5s17fq8KZ9jeFcbshhtZGF4cirR6gvevX9rXjLp+3bLMIiLRxRs3KUqWLMm+ffv45ZdfePjwIZ999hnly5fn1KlTrFy5kuHDh1OnTh0qV65M586d2b9/P/nz5w/P7CKRx88/M2jdz/QpF3i87lfY285x2s3NZPFik2++AcOwJKGIiIhIpLRgwQJsNhs9e/bk5s2b+Pr6MmHCBAzDYJY2KRURkQjWoAEcOQJNmz534loee6PiZDUOJ4MibWHgjSUEFMgHGzdaklVEJLp44yYFgIuLCz169ODgwYNUqFCBjRs3kj9/fvr370/79u1ZsGABK1euZPDgwWTMmDG8MotELv368deinnxbJfB46zf2tSsDOTub/PXXbapXtyaeiIiISGTw559/hlj38fHBMAy6du1KwoQJ8fDwoGXLlqRIkQIfH58ITikiIgJJksDUqbB4MaRMGeSEX0KYtgw29eCJE3SrAhUrX+F8g4owerRVcUVEory3alI8lTlzZtasWcOkSZOIFy8evXv3pkCBAmzZsiWs84lEbkOGMHVGdz6vFXj8zyewZlCwS8aONala1S/is4mIiIhEIh06dKB06dIcOXIkWD1NmjSYpkmXLl3w9vbm6NGj/PLLL1y+fJk02sRLREQsVLs27NsHZcsGrTrB+l9h9lzwi8vGDFCoTQCrB3eA9u3h8WOL0oqIRF1v3aS4dOkSO3bs4NKlSzRv3pxjx47RokULjh49Srly5WjXrh137twJj6wikcuIESz8+xta1QPTAI7UhyV/B7tk2DBo0cKKcCIiIiKRS5UqVdi2bRuFChXixx9/xM/P/hBHo0aNcHNzY9y4cRQqVIg8efLQq1cvDMOgdevWFqcWEZGYLnlyWLsWOnV67sTRD2DsDriRhRseUK0Z/Hz0L2yVKoJmAoqIvJU3blL4+fnx8ccfky5dOkqVKkW6dOlo1KgRcePGZcKECaxfv54sWbLw999/kzNnTmbPnh2euUWsNXo0a4Z/xccfQoATcLoizJth31ArUM+e8NVX1kUUERERiUxWrlzJtGnTSJgwIb/99hv58uVjw4YNZMyYkdmzZ5M0aVLHptlOTk506NCB7t27Wx1bRESEWLFgxAiYOBHc3IKcuJbbvk/FiRqYBvSsAHXSbuFWqfdh/36L0oqIRD1v3KQYOHAgc+bMcdw4mKbJnDlzGDTIvrRNuXLlOHDgAD/++CM3b96kcePG1KxZM9yCi1hm7Fi29OtA3cbw2AW4WBRmLoSAZ/9S+eIL6N3bqoAiIiIikVPjxo05duwYrVu35tSpU1SqVIlPPvmEUqVKcenSJfbt28e2bdvw8fFh5MiRODmFanVaERGRcNGyJWzZAsFWI/RLCNOXwOb/gQnLssH7NS+yr15x0AO8IiJv5I3/1T99+nQMw2DkyJEcOXKEESNGADB16lTHNa6urvz000/s37+f0qVLs3LlyrBPLGKlSZPY2+szajaBh7EAn1wwbTk8ieu4pEkTGD4cDMO6mCIiIiKRVcKECfn777/x8vIie/bsTJo0iZw5czJjxgzy589P8eLFSZQokdUxRUREQlS4MOzdC56eQatOsK4fzJ0Bj905kwhKNPdjQt+P4fvvwWazKq6ISJTwxk2Kc+fOkSZNGjp27EiOHDn44osvSJ06NefPn3/h2hw5crBx40bGjBkTpmFFLDVtGoe7taJqM7gbG7iVHqashodJHJfUrGmf/qmH/kRERERerUyZMnh7e9O7d2/u3r1Ly5YtqVKlCv/++6/V0URERF4pWTJYsyaEfSoON4LxW+B2WvxcoHU96Lj/N/zr1ALt3yoi8lJv/KPUVKlSceXKFVasWMHjx49Zvnw5V65cIWXKlC99z6effhomIUUst3Ytp79uQeXmcMMD8E0GU9bAvdSOS8qUsc/kjBXLupgiIiIikdn+/ftp27Yt1apVo23bthw5coSePXvi7e2Np6cna9euJW/evPTr14+AgACr44qIiLzU030qxo0DV9cgJ64WgjF74GwZAEYVhZqJVnC7dGE4ftyasCIikdwbNylq166Nv78/tWrVwt3dndq1a2Oz2ahTp0545hOx3pkzXP3kIyo1tXElHvAwAUxZBTezOi4pUACWLAEPD8tSioiIiERqe/fupUSJEowbN47Vq1czbtw4ihcvzr59+8iWLRvr169n/PjxuLu78/3331OoUCF27txpdWwREZFXat0avLwgRYogxQfJYPI62N0OgNVZoITnKU5XLgzLllmSU0QkMnvjJkXv3r0pUqRIsI2zixQpQm/tDizR2YMHPPqwHvWq3eZsIuCxO0xfCv8VcFySJQusXAkJEliWUkRERCTS++GHH/Dz8yNDhgw0bNiQDBky4Ofnxw8//OC4plWrVhw/fpymTZty8OBBSpcubWFiERGRN1OiBOzZA0WKBCnaYsGyP2HR3/DEjWNJoVhjXzZ1rAW//QamaVleEZHI5o2bFPHjx2fHjh1s2rSJqVOnsmnTJrZv3068ePHCM5+IdUwTs+1ntMlwgJ1pAP9YMHsuXHh2s5w6tX0dyuTJrYspIiIiEhX8888/xI8fn/379zNz5kz2799P/Pjx2bNnT7DrkiRJwuTJk1mzZg0ZM2a0KK2IiMjbSZ0aNm2C5s2fO7GvDYzfCrcycMMDKrWAiXO+h48/hgcPLMkqIhLZvNX2voZhULp0aZo0aULp0qUxDCO8colYb9gw+p2bzrR8gM2AhRPhVA3H6cSJYfVqyJDBqoAiIiIiUceTJ0+IFSsWHoHrY8aOHRsXFxf8/f1DvL5ixYocPHgwIiOKiIi8k9ixYdIkGDIEnIL+xO3K+/DXXjhZjSfO8Ek9+N+tOdgqV4KbN62KKyISabxRk6J169a0bt2a1atXv/U3WLVqFa1bt9Ym2hK1rF/PwrFd6VEp8NirNxxq4jgdNy6sWAG5clmSTkRERCTKyZMnDzdv3qRcuXL06NGDChUqcOvWLfLkyfPS97i5uUVgQhERkXdnGNC5M6xbB8mSBTnxKDFMWwYbeoPNiQGl4aM023lQvjRcumRVXBGRSOGNmhQTJ05k0qRJHDp06K2/waFDh5g4cSITJ0586/eKWOLcObw7fECzejb78aGPYFNPx2lXV1i4EIoWtSaeiIiISFT0448/ArBt2zb69+/P1q1bMQyDnj17vuadIiIiUU+5cvDPP1CyZNCqE2zsBdOWw/0kzM8FFYoexadCMTh+3KKkIiLWe6vlnkSivYcP+a9xbWrXuM19V+BKAfsyT0FMmAAVK1oRTkRERCTqqly5MgsXLqR8+fJkzpyZChUqsHDhQirqH1YiIhJNpU4NXl7w1VfPnThdFcbshSv52ZkGile/xLFaxe27b4uIxEAub3Pxt99+y7fffhteWUSsZZo8at+G+rkOciEB4JsMZiwCfw/HJd27Q5MmL/8SIiIiIvJytWvXpnbt2lbHEBERiTCxYsGwYVC8OLRpA/fvB564kx4mbIYPP+ZMthWU+OA2C1qUodyIJVCp0qu+pIhItPNWMylM03zrl0hUYY4YQdt709meFvB3hVnz4W46x/nateGXX6zLJyIiIhJVnT9/nvPnz+Pr6/vW7/X19XW8X0REJKpq1Ah27YIcOYIUH8eDGUtgd3tuu0OVDx8xpVs1mD3bspwiIlZ44yZFaBsOalRIlLBxIwPmdWZKfsAElo2CC6Ucp3PlgqlTwUkLpImIiIi8tQwZMpAxY0bGjBnz1u/966+/yJgxI5kyZQqHZCIiIhEnVy7YuROqVQtSNJ1h2WhYNZAnhkGLugH8NPpjzD//tCyniEhEe6PlnjZs2BDeOUSsc/48i7+tQ/cagQ21nZ1g36eO04kSweLFED++RflEREREYjg9+CQiItFF/PiwZAl06gTB+hDbu8LtjFC/Ob3LPeTG4s8ZdvsWTt91tyyriEhEeaMmhaenZ3jnELHGw4ccaFmNJpXvYhrA6UqwaqjjtLMzzJ0LmTNbF1FEREQkuli9evVbL/m0bdu2cEojIiJiDRcXGDUKsmSBb78FRy/+6AdwNw00rsPIYj483NaDP7vfxvm3fmAYlmYWEQlPb7Vxtki0Ypr4dGxF7QJHue8K3MgCc2bbp1oGGj4cKlSwLqKIiIhIdLJmzRrWrFljdQwRERHLGQZ88w1kyADNmsGjR4EnLhWDsTugeWXGvn+aR94DmNDhNi6/j7I/SSkiEg1phX2Jsfz+GE4DYzbnEwKP4sOMxfAokeN827bQoYNl8URERESiHdM0Q/USERGJrj74ALy8IGnSIMXbGWHiRriWnan5ofGNMTxu1hiePLEqpohIuNJMComRzI0babe+C1vzAzYnmDcdrud0nC9TBkaO1GxKERERkbBQtmxZDP3DSkREJETFitk31K5RA44dCyzeSw0TvaBFJebmPozf8TnMrn+X2LPng4eHlXFFRMKcmhQS81y8yKDfajKpZOBTeet+g5M1HafTp4d588DV1aJ8IiIiItGMl5eX1RFEREQitYwZYcsWqFwZ9u0LLN5PAZM2QItKLMl+gLqnVrGgZhU8Fi2378AtIhJNaLkniVkCAljyRWX+V+K+/di7KWz9n+O0hwcsWvTcNEsRERERERERkXCWJAmsWwdFiwYpPkgKEzfApfdZnQVqZNjKveoV4fZtq2KKiIQ5NSkkRrk6egAtchzDNICLRWDx2GDnJ0+G/PmtySYiIiIiIiIiMVuiRLBmDZQsGaT4KDFMXgcXirExA1TNuYfbVT3hxg2rYoqIhCk1KSTmuHKFzlt7ctsduJsSZi6EgNiO07172zesEhERERERERGxSvz4sGoVeHoGKfolgClr4FxptqeFigUOcKNaWfDxsSyniEhYUZNCYowVPzRiZg5/eBLb3qDwTeU498EH8OOP1mUTEREREREREXkqblxYvhwqVQpSfBwPpq6EM+X4JxWUL3KE/6qVgStXLMspIhIWQr1x9tGjRxkxYgR79uzh1q1b2Gy2F64xDIPTp0+/U0CRsHB/+SI+j7/JfrBsFFx+tsBj/vwwaRI4qWUnIiIiIiIiIpGEhwcsWQINGsCKFYHFJ3Fg2jJoXIeDmddRruQJ1lYrReplmyBNGkvzioiEVqiaFBs3bqRatWo8fvwYANM0g503DAPTNDEM490Tiryrhw/pNbEl53IDJ6rD/k8cp5ImtW+UHSeOdfFEREREREREREISOzYsWAANG8LixYFFfw+YvhQa1eNY1lV4ljvDuuolSL9kM2TIYGVcEZFQCdWz4927d8fPzw94sUHxspqIVfb9+gXDct4Bvzj2WRSBnJxg7lxIn97CcCIiIiIiIiIir+DmBnPm2GdUOATEhpmL4HhNTieGspUucqpWSfj3X8tyioiEVqhmUuzfv98xW6JevXpkz54dNzc3zZyQSCfg8EE+uz6BgJSA109wJ4Pj3JdfQtmylkUTEREREREREXkjrq4wcyY0awazZwcWA9xg1nz4qCHncy7Cs+oV1tctRfZFWyFTJkvzioi8jVA1Kdzd3fHz86NFixZMnDgxjCOJhBGbjZG/1WNvNhMuF4QdXztOpUsHP/9sXTQRERERgStXrvDPP/+8dI87gBYtWkRwKhERkcgpViyYNu3Z/wJgc7VPs/igMZdzz6Nc1ausr1uKnAu3QObMluYVEXlToWpSVKhQgfnz55M8efKwziMSZs6PHcwPGf6FAGdY8jeYzo5zf/wBceNaGE5EREQkBvPz86NNmzbMmDHjtUvFqkkhIiLyjIsLTJpkb1Q4nhu2xYK5M8HWjKt5Z1Gu2lXW1ytF7gVbNKNCRKKEUO1J8euvvxInThwmTpzI4cOHwzqTyDszfXzouO177rsCuzrBlfcd5z76CGrVsi6biIiISEz33XffMW3aNGw220ubFNrnTkREJGTOzjBuHLRpE6RousD8qXC8Fj5xoVz1/zjQoBScOmVZThGRNxWqmRT9+vUjU6ZMHDhwgAIFClCiRAnSp09PrFixgl1nGAbjxo0Lk6Aib2Ne74YszfgEbqeD9b846gkSwPDhFgYTEREREWbPnu3Yz840TTUkRERE3pKTE/z1l31GxejRgUXTBebMhmZVuZ5hM+Wr+7Dmw9Kk/XMhJEtmZVwRkVcKVZNi4sSJGIaBYRgEBASwdetWtm7dGuwa0zTVpBBL3F67lC89NoIJLPsDnsRxnOvfH1KmtC6biIiIiMCNGzcAyJQpE/PmzSN79uy4ublZnEpERCRqcXKyL2cdEABjxgQW/d1hxhJoVY6bKfdTqcY1VrarQ5I5WyBHDkvzioi8TKiWe3rq6RNPT59+CvoSsYSfH90nNuNKPODwR3Dy2bpOpUrBZ59ZF01ERERE7LJmzQpAkyZNyJcvnxoUIiIioWQYMGqUfWlrB78EMGUVXM/KLXeoUusGuxuVgZMnLcspIvIqoZpJUbZsWcf0bJHIZFv/L/gz6x14mBBWjHDUY8WyT4N0eqe2nIiIiIiEhc8++4yvv/6aPXv2WB1FREQkynN2hilT4PZtWLMmsPggGUxZA61LcSfBJWpUvc6WeqXIuXArBD4sICISWYSqSeHl5RXGMUTe3eNjh2nrMw6SAmv7wf0UjnPffQe5c1uXTURERESeqVWrFnPmzGHlypU0atSItm3bhrjHHUC6dOksSCgiIhK1uLnB/PlQqRLs3BlYvJMepq6CT8py0+MmVateY3vNsqRevhmyZLE0r4hIUKFqUohEOqbJwP51OZzBhHOlYG87x6ls2aBHDwuziYiIiEgwWbJkwTAMTNNkzpw5zJkzJ8TrDMPA398/gtOJiIhETXHjwrJlULYsHDkSWLyWG6YthxaVuJDAl2qVrrK5qicJV2+CzJktzSsi8tQ7LX5z7do1+vbtS+3atSldujS1a9emb9++XLt2LazyibyRk5OG8nOa0+DvCkvGBDv3118QO7ZFwURERETkpZ4uIRvSHnfa605EROTtJUkCq1dD+vRBipeKwaz54B+LQ8mhbtnLPKroCadPW5ZTRCSoUDcp1qxZQ7Zs2fjhhx9Yvnw527dvZ/ny5fzwww9kzZqV1atXh2VOkZcyb96k3dbv8HMBtnaD67kc5z75BMqVsyyaiIiIiLyEGhEiIiLhI3Vq+94UyZIFKf5bGRaPAxM2ZYBmxS4RUKEc/PuvRSlFRJ4J1XJP586d48MPP+TevXvBnn56+uu7d+/y4YcfcuDAATJkyBBmYUVCMvnnD9iQ5glczwabfnDUkyaFgQMtDCYiIiIiIdqwYYPVEURERKK1rFlh5Urw9DS5d8/+8zoONIf4F6FSD+blgq98LzKyfDkMr42QMaOleUUkZgtVk2LIkCGOBoVpmmTMmJHkyZPz33//cebMGQDu37/P0KFDGT58eJgGFgnq2sblfOPqBSaw5C8IcHOcGzbMPs1RRERERCIXT09PqyOIiIhEewULwrx5JjVrwpMngY2KLd0h3iUo9gd/FIVU9y7Qo2pV2LYN3nvP2sAiEmOFarmnp0s5xY8fn02bNnH69Gm2bdvG6dOn2bhxI/Hjxw92nUi4ePKEbyY15YYHsO8TOFfOcapKFWjc2KpgIiIiIvKmnjx5wt69e1m7di179+7lyZMnVkcSERGJNipWhGHD7gQvrhwBR+oD8H1FGB/3JNSuDQ8eWJBQRCSUTYoLFy5gGAatW7emdOnSwc6VKVOG1q1bY5om58+fD5OQIiFZN/wrpqS/Db5JYfUgR93dHUaPhsDVx0REREQkEgoICKBHjx4kSZKEokWLUrVqVYoWLUqSJEno3r07/v7+VkcUERGJFho0eMSAAbZnBdMJ5k2Hc6UAaFsblt7cAU2aQECARSlFJCYLVZPCZrN/sLm4hLxa1MvqImHFfPiQ7mfG2g9WDYVHiR3neveGTJmsySUiIiIib6Z+/fr0798fX1/fYJto+/r6MmDAAOrXr291RBERkWijSxf46qsghYDYMGMxXMtBgBM0/Ah27F0EnTqBaVqWU0RiplA1KdKkSYNpmkyaNIkTJ04EO3f8+HEmTZoEQOrUqd89oUgINoz7gd3JnsDJqnCwqaOePz907mxhMBERERF5renTp7N06dKXnjdNk+XLlzNt2rQITCUiIhJ9GQYMGQIffRSk+CgxTF0Jd1PyMBbUbALH5oyG/v0tyykiMVOomhQVKlQA4Nq1a+TOnZsSJUpQt25dSpQoQZ48ebh27RqGYTiuEwlTAQH0OzgaHnvAstGOsmHAmDEQK5aF2URERETktZ4+1ARQsWJFJk2axMqVK5k0aRKVKlUK8ToRERF5N05OMHkylC0bpHgnPUxdBQ8ScdMDqjaDy791h6lTLcspIjFPqNZl6tKlC5MnT8bPz4+AgAB27drlOGcGTglzc3Ojsx5pl3Cwd/og1qR6COv7wO2MjnqnTlC0qIXBREREROSN7Nu3D8MwKFeuHGvWrAl2rnnz5lSoUAEvLy/2799vTUAREZFoKnZsWLgQypSBw4cDiz55YfoyaFGJ8wkfUK0ZbPr8ExKmTGnfeVtEJJyFaiZFtmzZmDhxIq6urgDB1pAFcHV1ZcKECWTPnj3skooAmCb9tvSHhwlgx7PFFNOkgV9+sTCXiIiIiLyx27dvA1A22KOcz3h6egJw586diIokIiISYyRKBCtWQNq0QYoXS8DMBeDvysHkUO9Dfx59WA8OHLAqpojEIKFqUgA0bNiQgwcP8tVXX1GkSBGyZMlCkSJF+Oqrrzh48CAff/xxWOYUAeDEssnMS3kLdneEx/Ed9V9+gXjxLAwmIiIiIm8sfnz7v+N2794d4vmnM7Xj6R94IiIi4SJtWlizBpImDVL8twrMnwI2JzZmgGZVfAmoUwuuXrUqpojEEKFuUgBkyZKFoUOHsnPnTk6cOMHOnTsZOnQoWbNmDat8b2XBggVUrlyZJEmS4O7uTsaMGWncuDEXLlwIdt3du3fp0qUL6dOnx83NjfTp09OlSxfu3r1rSW55cwOXfY/5xAN2fO2opU8PTZpYl0lERERE3k7+/PkxTZMVK1bQvHlzVq1axb59+1i1ahXNmjVjxYoVGIZBvnz5IjSX7idERCQmyZ4dVq587qHPIw1h6WgwYV4u+DbHBahXDx4+tCqmiMQAodqTIrIxTZP27dszZswYMmfOTKNGjYgXLx6XL19m48aNnDt3jrSBc9ju37+Pp6cn+/fvp3LlyjRu3Bhvb2+GDh3Khg0b2LJlC3HixLH4dyQhubR1JZOSXoLdX8KDZ63+bt20WbaIiIhIVNK4cWM2bNgAwPTp05k+fXqI1zWJoCdRdD8hIiIxVaFCsGQJVKsGjx4FFv9pC+43oXJ3hpaA3It28mnr1jB9OhiGpXlFJHp6oyZF69atAWjUqBFVqlRxHL+OYRiMGzcu9One0MiRIxkzZgwdO3Zk+PDhODs7Bzvv7+/v+PWAAQPYv38/3bp1o3///o56r1696NOnDwMGDOCnn34K98zy9obN+ponCWLB1m8dteTJ4ZNPLAwlIiIiIm/tk08+YcqUKWzevBnDMBx724H9HgKgVKlSb3zf8a50PyEiIjGZpyfMng3160NAQGBx63f2RkXpgXxeC7JMnolnnxzQq5elWUUkejLMoHcEL+Hk5IRhGAwcOJAuXbo4jl/FNE0MwyDA8ekWPh4+fEiaNGlImDAhx48fx8Xl5X0X0zRJkyYNd+/e5erVq8GecHr06BGpUqXCw8ODCxcuvPb391Tu3Lnx9/fn6NGjODm90+pZ8gq3jvxDuqnv43u4NSx+1vjq398+kyIys9ls+Pj4kCxZMo0RCZHGiLyKxoe8jsaIvE7u3LkBOHz4sMVJgnvw4AFffPEFU6ZMCXbP4OzsTPPmzRkxYgRx48YN9xy6n5DITp/z8joaI/IqbzM+pk6F5s2fK9b9BApOJMkD2PU3ZBo1Axo1Cr/AEuH0GSKvElH3Eu+03NMb9DfC3Zo1a7h58yatWrUiICCAxYsXc+LECRImTEilSpXIkiWL49qTJ09y+fJlqlat+sIU7NixY1O2bFkWLVrEqVOnLNtXQ0I2anx7fOM4wZbvHLWECeHzz63LJCIiIiKh5+Hhwfjx4xk0aBA7d+7k1q1bJEqUiKJFi5IkSZIIy6H7CREREbtmzeDmTfjqqyDFJWMg/kVuZF5L7cawvW1L4mfIAMWLWxVTRKKhN2pSlC1bFsMwHOuwPj2ODPbs2QOAi4sL+fPn5/jx445zTk5OdO7cmUGDBgH2mwrgpTcMT+snT5586TV+fn74+fk5jm02G6ZpYrPZ3v03IyF6cOkMw5122zdvuvnsv0unTiZx4phE9v/rNUbkdTRG5FU0PuR1NEYkqkucODHVq1e37PvrfkIiO40ReR2NEXmVtx0fX3wB168b/Pxz4M/9bLFg1jxoXZojKQ7SqM5jFtevi9P2nZAuXTgml4iizxCJDN6oSeHl5fXKYyv5+PgAMHjwYAoVKsSuXbvImTMn+/bto23btgwePJjMmTPz+eefc+fOHQASJEgQ4teKHz8+gOO6kPTt2/eFNWYTJUqEj4+PpkSFk+kjPuVaHGBzD0fNw8NGo0bX8PGxfjbP69hsNu7cuYNpmhojEiKNEXkVjQ95HY0ReR1/f/9XLmEUEc6fPw/YGxJx48Z1HL+JdOH8AxDdT0hkp895eR2NEXmV0IyPzz+HkyfjM3Omh73wOD5MWw5tirMi6yW6Xfehf82a3Fy0CDMClmaU8KXPEHmViLqXsPZuJQw87fK5urqycOFCUqVKBUCZMmWYO3cu+fLlY/DgwXweRusCde/enS5dujiOixUrRkBAgNZtCydPbt1gRMAmOFET/svvqLdrZ5AjR1ILk705m82GYRgkTZpUY0RCpDEir6LxIa+jMSKvY3WDAiBDhgwYhsGgQYPo3Lmz4/h1DMMItml1eND9hER2+pyX19EYkVcJ7fiYOBGuXzdZuzbw7+t7aWD6MvikDENL3CO3zxFaf/015oIF4OwcPuElQugzRF4lou4lQv1dHj9+DNj/MQ/2jeIGDBjA3r17SZgwIZ06daJw4cJhk/IVnj7FVLhwYccNxVO5c+cmU6ZMnDp1itu3bzuufdmTTXfv3g32NUPi5uaGm5ub49jJyQmbzYaTk5P+IIeDuX9/xbn4Jsz53lFzdYVvvjFwcoocS469CcMwNEbklTRG5FU0PuR1NEYkqnh+T7vIsMed7ickKtDnvLyOxoi8SmjGh5sbzJsHZcrAgQOBxf/yw+y50LQm7Wv5k3nKMsp17w6ByyJK1KXPELFaqEZev379cHd3x8PDwzFVu3r16vz0008sXbqUqVOn4unpGe67fgNkz54dgIQJE4Z4/mn94cOHwdaIDcnr1piViGU+ekS/K3PgrCdcLOGot2oFqVNbl0tERERE3l5kbFCA7idEREReJn58WLbsuZ/B/FsFlv6JvxM0+BhOTBwMY8dallFEoodQzaTYuXMnpmmSM2dO0qVLx6FDh9i4cSOGYThuNh49esSQIUMYN25cmAZ+Xvny5QE4evToC+eePHnCqVOniBMnDkmTJiVFihSkSpWKrVu3cv/+feLEieO49tGjR2zatIlUqVKRJUuWcM0sb2bZ2G4cSuwPS5/tReHkBN26WRhKRERERN7ahg0bAMicOXOw48hA9xMiIiIvlyYNLF8OpUvDvXuBxX2fQsKz3PL8hZpNYEeX9iTJnBkC/04VEXlboZpJcfDgQQzDoFixYgBs2rQJAA8PD7788kvixImDaZqOenjKnDkzVapU4dSpU4x9rnPbr18/bt++Tf369XFxccEwDNq0aYOvry99+vQJdm3fvn25desWbdq0eaP1cSWc2Wz0OzYWLhW2d+kDNWoEgfe2IiIiIhJFeHp64unpSZo0aYIdv8krvOl+QkRE5NXy5bMv/RRsafoNP8PBjzmVBBp8EMDjjxrAS2Yaioi8TqhmUly7dg2AtGnTAnDkyBEA6tevz7Bhw7DZbPz+++9cunQpjGK+2qhRoyhZsiSfffYZCxcuJEeOHOzbt4/169eTPn16Bg4c6Li2W7duLF68mAEDBrBv3z7ef/99vL29WbFiBQUKFKCbHtOPFLZM78fWpA9hZvdg9e++syiQiIiIiIS7hw8fcuzYMRImTEjGjBkj7PvqfkJEROTVKleGMWOgdesgxUXj4b1jbMrgTdsyt5lQqybGjp2QKJFlOUUkagrVTIqHDx8Cz3b3PnnyJIZhkCtXLgDHhnMRtc5s5syZ2bNnD61atWLv3r2MGDGCkydP0rFjR3bt2kWKFCkc18aJEwcvLy86d+7MsWPHGDx4MIcOHaJz5854eXkFm7ItFjFN+m0fAD454VgDR7lOHcib18JcIiIiIhImdu3aRY8ePejRowdXrlwBYPbs2SRPnpzChQuTJUsWGjZsSEBAQITk0f2EiIjI633yCXz/fZCCvwfMXAj3kzCpAPRNfhI+/BCePLEooYhEVaGaSZE4cWKuXbvG0qVLqVKlCtu3bwcgW7ZswLOZFkmTJg2jmK+XNm1aJkyY8EbXJkiQgCFDhjBkyJBwTiWhcWDZeJYluwPzRwSr9+jxkjeIiIiISJQyZcoU/vjjD+LGjctPP/2Er6+vYxmlp/vczZs3jz///JOOHTtGSCbdT4iIiLxenz7g7Q1LlwYW7mSAObOheRW+rxhA1tnr+eiLL+DPP0HLH4rIGwrVTIqCBQtimia7d++mRIkS+Pr6AlC8eHEALly4ADxbDkrkbQxY+SPcygAHmzhqFStC4BYoIiIiIhLF7dq1C7DvTRErVizWrVsXrEEB9lnZs2bNsjKmiIiIPMfJCaZOhezZgxTPVoA19qURW9SHncvGwBs2/kVEIJRNis8//9zx66c3EdWrVydVqlT4+/uzbt26YBtri7ypM1uXMTPJFdj6LZjPJvpoFoWIiIhI9HH+/HkMwyBLliwA7Nu3D7A/DHXz5k1KliwJPNv7TkRERCKPBAlg0SKIHz9IcUdn2N+cR7GgTmO49L8O9ikXIiJvIFRNijp16jB+/HiKFClCvnz56NixIzNmzADg+PHjFC1alCpVqlC7du0wDSvR3+A5nQm4nwL2PduJqVgxKF/ewlAiIiIiEqZu3rwJ4Njr4cSJExiGQfny5UmYMCHVqlUD4O7du5ZlFBERkZfLnh2mTXtuRaclY+DS+/jEhUa1/XjS8EPQ3+Ui8gZCtScFQKtWrWjVqtUL9dy5c7NixYp3ySQxlM+R3YyLexI2DICA2I56jx5axlBEREQkOokVKxb+/v5cvnwZgAMHDgCQNWtWAPz9/QGIGzeuNQFFRETktWrVsu9R8eOPgYWA2DBrAbQtzJb0PvS4eIqBbdrArFn6wY6IvFKoZlKIhIcREz/n0ZNEsPvZcmJ58tj/0hMRERGR6CNDhgyYpsnYsWOpUaOGY1mnvHnzAjiaF8mTJ7cso4iIiLxejx7QoEGQwt20MHsuBLgwqBQsPDgHRo2yLJ+IRA1vNJOiT58+AFSpUoXixYs7jt9Ez549Q5dMYpS7F0/zh/Ne2NETnjx7Yq57d/umTCIiIiISfVSrVo0jR47w6NEjVq1aBcB7771H0aJFAfD29sYwDPLkyWNlTBEREXkNJyeYOBGOH4fDhwOL58vA6oFQvTOt6sHen74mc9GiUKSIhUlFJDJ7oyZF7969MQyDuHHjUrx4ccfxm1CTQt7EX2PacduICzu+ctQyZYKGDS0MJSIiIiLhonv37ixdupQTJ04A4ObmxsiRI3F2dub8+fPs3r0bgFKlSlkZU0RERN5AvHiwcKG9B3H7dmBx59eQZgd38s7iwwb+bGv8Ie6790OiRJblFJHIK9R7UgCYpvnK82/ayJCY7dHt6wx5tB68O8OjxI76//4HLu80QkVEREQkMkqSJAne3t5s2LCBR48eUaRIEVKnTg1AvHjx2L59OwDZs2e3MqaIiIi8oSxZYOrU55bsXjwWkh9gf8qjfJXrPGNatoRFi7Q/hYi84I1+BJwuXToMwyBBggTBjkXCwoLJ3bnq6grbvnHUUqaEli0tDCUiIiIi4crNzY1q1aq9UE+UKBHFihWzIJGIiIi8i5o17Zto//xzYOFJXJg1Hz4ryt/v36P0giW0GDQIvv3W0pwiEvm8UZPi7NmzrzwWeRfTTy6Asy3BN5Wj1rUruLlZGEpERERERERERN5Kr16wcyesXh1YuJEDFo2Hhh/RvhYUGvIdeUqWBC3pKCJBaEtisdTN0wdZmeA2bPmfo5YkCbRta10mEREREQlbTk5OuLi4MHToUMexs7Pza18uWvtTREQkSnF2hmnTIF26IMWjH8L2LjyMBR9+aON+04/g2jXLMopI5BOqJsXly5fZtGkTmzZt4tatW8HO3bp1y3Hu8uXLYRJSoq95s/vgf7IO3M7kqH31FcSNa2EoEREREQlzpmm+sKfd09qrXiIiIhK1vPcezJ0Lrq5Bimv6w9kyHH8Pvs53BZo1g4AAyzKKSOQSqiZF7969KV++PPXq1Xvh6SZXV1c+/PBDypcvT69evcIkpERf0y+thAPNHMfu7iZffGFhIBERERGJEGpAiIiIRF9FisCIEUEKpgvMnQX3UjD2fZh3YTX89ptl+UQkcgnV/OktW7YAUKdOHeLFixfsXJw4cahTpw7jx49n06ZN755Qoq1Le73wiuMCJ2s6anXrGiRKZGEoEREREQlzEyZMAKBo0aLBjkVERCT6atsWtm+HSZMCC74pYc5saFmBz+r4U3RwT9KWLAkVK1qaU0SsF6omxcWLFwHInDlziOczZMgAoOWe5JVmL/oNjjWAgGc7ZDdtamEgEREREQkXLVu2fOWxiIiIRD+GAaNGwf794O0dWDxfBtb25VbVb2leH9Y1bYzzPm9ImdLKqCJisVAt9+Tv7w88a1Y878KFCwAEaG05eRnTZMatTXDgWVcicWKTKlUszCQiIiIiIiIiImHGwwPmzYMECYIUt3eFI/XZmAH6Z7sGjRpB4M8aRSRmClWTIlWqVJimyYwZMzhz5kywc2fOnGHGjBkYhkFKdUHlJU6tn8tu1yRwtpyj1rChEXxTJRERERGJliZOnEiFChWoUKECZ8+eDXbu/PnzVKxYkQoVKjBx4kRL8omIiEjYyZwZJk9+rrhoAtzIQs/ysPP0JujZ05JsIhI5hKpJUapUKQB8fX0pUKAAnTp1YvDgwXTq1ImCBQvi6+sLQOnSpcMuqUQrM1cPgUONCDoEtdSTiIiISMwwdepUvLy88PPzcywV+1S6dOkA8PLyYpJjEWsRERGJyurUgf/9L0jBLwHMnkuAvztNPoB7g/vCsmWW5RMRa4WqSdGhQwcMw8AwDO7du8eoUaPo1q0bo0aN4u7duwAYhkGHDh3CNKxED+aTJ0x/tDvYUk/p0pmULGlhKBERERGJMEeOHMEwDEq+5B+ARYoUAeDo0aMRGUtERETC0S+/gKdnkMJ/+WH5H/ybCL6oAbRsCdrfViRGClWTolixYnz//feYpolhGCFe88MPP1CsWLF3CifR04ElYzkakBWuFnLUmjQxcArVaBQRERGRqObGjRsAxI4dO8TzLi4uANy6dSvCMomIiEj4cnGBmTMhRYogxf2fwD+fMrkAzEh5w96osNmsiigiFgn1j4X79OnDjBkzKFCgAACmaQJQsGBBZsyYQe/evcMin0RDMzb/EWwWBWipJxEREZGYJG7cuACsX78+xPMbNmwIdp2IiIhEDylSwKxZ4OwcpLj8d7hSgM9rwYVda2HIEMvyiYg13unZ9Y8//pi9e/fi6+vLxYsX8fX1Ze/evXz88cdhlU+iGfP+fWZwBA42cdTy5oU8eSwMJSIiIiIRKnfu3JimyY4dO+jUqRPXr18H7DMsvvzyS7Zv345hGOTKlcvipCIiIhLWypaFvn2DFAJiw+y53DET0Koe2Hp0h3/+sSqeiFggTBbYcXd3J0mSJLi6uobFl5NobPucIZy/VwxuZ3LUNItCREREJGapW7eu49ejRo0iefLkxIsXj2TJkvHHH384ztWvX9+KeCIiIhLOunaFevWCFG5lhoWTWJ8RRhbyh8aN4f59q+KJSAR7pybF2rVrqVatGvHjx8fDw4Phw4ezbds2+vTpQ58+ffDz8wurnBJNzNg94YWlnho3tiiMiIiIiFiiffv2ZMr07KEV0zS5f/++YwlZgEyZMtG+fXsr4omIiEg4MwyYMAEyZw5SPF4Xtnfhu0pw5NYJ+Pprq+KJSAQLdZOib9++VK1alTVr1uDr6+uoJ0qUiN69e/PTTz+xatWqMAkp0YP/dR9muV2Aww0dtbJlIV06C0OJiIiISISLEycOK1euJGfOnMEaE2BvWOTOnZuVK1fi4eFhUUIREREJbwkTwrx5EDt2kOKa/jy6UpLm9eHxhLH2C0Qk2gtVk8LLy4vvv/8e4IWbipw5c5IzZ04AFi1a9I7xJDpZP+M3rvlUggfJHLUmTV7xBhERERGJtrJkycKBAwdYtGgR3bp1o02bNnTr1o1Fixbh7e1N5mCPVoqIiEh0lD8/jBwZpGC6wJxZ/JPgPX4uC3z2GVy4YFU8EYkgLqF507Bhwxy/btSoETNnzgx2vnTp0hw9epR9+/a9UziJXmYcngkHBjmOY8Uy+egjw8JEIiIiImIlJycnateuTe3ata2OIiIiIhb59FPYtAmmTAks3EsD86fya5Pq1Dx5i+LNm8O6deDsbGlOEQk/oZpJsX37dgzDoEGDBkyfPv2F8+kC1++5dOnSu6WTaOPRmZPMi3MPjtVz1KpXN0ic2LpMIiIiImK9HTt2MGTIELp3787mzZutjiMiIiIRzDBg9GjIlStI8XRVzC09aF4f7m/bCH37WpZPRMJfqGZS3Lp1C4CCBQuGeP7JkycA3LlzJ5SxJLpZMfMX7p2rA0/iOmpa6klEREQk5rp48SKNGzdm27ZtjlrSpEk5d+4cLVu2xDAMjh49StasWS1MKSIiIhEhThyYOxcKF4YHDwKLXj9xKu02ulbZwOhevewbm5Yta2lOEQkfoZpJkTBhQgDOnDkT4vkdO3YEu05kxpnFcKCp4zhuXBPN6hcRERGJme7evUv58uUdDYqg+9x99NFHeHh4YJomCxYssCqiiIiIRLCcOeGvv4IUTGeYN50/c6RgRSYbNG4MPj6W5ROR8BOqJkX+/PkxTZNp06Yxa9YsR93Hx4effvqJ1atXYxjGS2daSMxy13sXi+M6w+mqjlqDBgYeHhaGEhERERHLDBkyhNOnTwPBGxQAbm5ulCtXDtM0Wb16tRXxRERExCLNmkHbtkEK91PA3Bm0quOMz53L0Lw52GyW5ROR8BGqJkWzZs0A8PPzo0ngmj2maTJw4ED69OnzwnUSsy2a+yt+Jz4CWyxHTUs9iYiIiMRc8+fPByB16tQcO3bshfN58+YF4Ny5cxGaS0RERKw3fDgUKBCkcK4cPnt68WkdMFevhn79LEomIuElVE2K5s2bO55uAjAMA8Mwgl1Tvnx5mjZtGtLbJSYxTWZcXRNsqadkyUwqVrQwk4iIiIhY6vTp0xiGQfPmzcmWLdsL5+PHjw/A5cuXIzqaiIiIWCx2bJgzB+LFCzLbcnMPlrqXZHQR4McfYdMmy/KJSNgLVZPCycmJZcuW0aZNG5ycnDBN0/FycnLi008/ZfHixWGdVaKg65tWsto9GVwo7ag1amTgEqot20VEREQkOnFzcwux/rQ54ezsHJFxREREJJLIkgXGjQvyQLTpDPOn0MUzHkeSaH8KkegmVE0KAHd3d8aMGYOPjw/Lly9n6tSpLFu2jP/++4+///4bD204IMDcpQMIOBx8bSct9SQiIiISs6VLlw7TNFm4cCH+/v7Bzvn4+DB79mwMwyB9+vQWJRQRERGrffQRtGoVpHA7E35rh9PkA/D7T/tTiEQnb92k8PX1pXXr1rRu3Zr+/fuTKFEiqlWrRpMmTahevTqJEycOj5wSFT15wvTbW+Hgs6WeMmc2KVrUwkwiIiIiYrkKFSoA4O3tTeHChR31qVOnkj9/fnwCn4ysVKmSJflEREQkchg+HDJmDLLs0/5P8L7xAT0qAtqfQiTaeOsmRdy4cZk2bRqTJk3izp074ZFJookLy2ey2SUnXMvtqDVtavDc9iUiIiIiEsN06dIFd3d3AA4ePOjY387b25v//vsPsM/c/vLLLy3LKCIiItaLHx+mTDFwcgrSqFj6F0PypGJ1Zuz7U2zebFk+EQkboVruKUOGDPY3O4V6tSiJAWatHRZsw2zQUk8iIiIiApkzZ2bKlCm4ubk59rYDHP/r5ubG5MmTyZgxo5UxRUREJBIoVQq6dw/yxOvDJLBwAi3qGlyPbYOWLeHePesCisg7C1WXoXnz5pimybJly15YQ1YEgAcPmP7EGw41dpTef98ke3YLM4mIiIhIpNGgQQMOHz5M586dKVq0KJkzZ6ZIkSJ89dVXHD58mAYNGlgdUURERCKJXr0gyAqR8G8V/jvyBZ/WAfPMGeja1bJsIvLuXELzpkaNGrFq1Sq2bdtG5cqV6dy5M9myZQtxs+x06dK9c0iJek7M+4t9fqXgblpHrWlTrfMkIiIiEtP5+/tz+fJlAGLHjs3gwYMtTiQiIiKRXaxYMHUqFCxo8vBh4M+X1gxgcbt1jHn/CO3GjIH69aFaNWuDikiohKpJkS1bNgzDwDRNNm3axKZNm0K8zjAMzbSIoWZs+RMOfOM4NgyTRo3UpBARERGJ6QICAsiQIQOGYdCoUSOmTZtmdSQRERGJArJnhyFDDD7/PLAQEBvmTaNzy2JUOPOYrJ9+CocOQaJEluYUkbf3TptKPN3g7uk6siG9JOYxr19nqstZOPKRo1ahAqRMaV0mEREREYkc3NzcSJgwIQC5c+e2NoyIiIhEKe3aQc2aQQr/FeDh1j40bwD+Vy9Dp06WZROR0At1k0KNCHmZfTOHcupmNXj0rHOtpZ5ERERE5KnixYsD8O+//1qcRERERKISw4Bx4yBp0iA/j9z6LTsDStO3NDBtGsybZ1k+EQmdUC33tGHDhrDOIdHIjH8mw4Fnawu7utlo0OCdJu2IiIiISDTy22+/sWnTJqZOnUrDhg2pUqWK1ZFEREQkikieHMaONahb92nFCRZMone7/FQ75UuR9u2hdGn7hSISJYSqSeHp6RnWOSSasJ0/x3T3O3CitqNWu5YTCRJYGEpEREREIpURI0aQPXt29u3bR/Xq1cmfPz/ZsmXDw8Mj2HWGYTBu3DiLUoqIiEhkVacOtG4N48cHFm5nwrZmCM0btOWfv67j0a4dLFhgn3ohIpHeWzcpnjx5wtatW7l69SopUqSgZMmSuLq6hkc2iYK2TuvH5cv1wd/dUWva1MJAIiIiIhLpTJw4EcMwMAwD0zTx9vbG29s72DWmaapJISIiIi81dCisXw9nzwYW/vmM49kX063yUn5ftAimTIEWLayMKCJv6K3W4Fm/fj0ZMmSgYsWKNG3alIoVK5IxY0bWrl0bXvkkiplxbA4ceNaViJ/ARo0aFgYSERERkUjr6f52Qfe70753IiIi8ibix4dJk8Awgvy7YfFY/sj9HiuzYN9E+8IFy/KJyJt745kUZ86coU6dOjx48CBY/cqVK9StW5eDBw+SKVOmMA8oUUfAmX+ZFccFzlR01D760Ak3NwtDiYiIiEikU7ZsWQwtvyAiIiLvqGxZ+OYbg0GDAgv3k8OSv/ikzgccGn2XJC1bwtq14KS9UkUiszduUgwdOpQHDx44pmQH9ejRI4YNG8aIESPCPKBEHXuW/c3NfxuB6eyoaaknEREREXmel5eX1RFEREQkmvj5Z1i5Eg4dCiwca8DVHM1pX2sKs+dswBg6FL75xtKMIvJqb9xGXLdunePXTZs25ffff6dJkyYhnpeYaeXhhXDw2ZhIkdIf7bEuIiIiIiIiIiLhJXZsmDoVYsUK8lD1ipHMTZ2W6XmBHj3gub2vRCRyeeMmxfnz5zEMg1q1ajFlyhQ6dOjA1KlTqV27NqZpcu7cufDMKZHdkycsvncfLhd1lJo2cdFsOhERERFxCAgIYOTIkZQrV44cOXJQrlw5Ro4cSUBAgNXRREREJArLnx/69AmylKRfAlg4kU7VDK66PrYv9fHokXUBReSV3vhHyPfv3wegRIkSwerFixcH4OHDh2EYS6KaG5tXse9u5WC1Bg0sCiMiIiIikVK9evX4+uuv2bx5MydOnGDTpk18/fXX1KlTx+poIiIiEsV9+y2ULBmkcLYCt7y/pkNNMA8fhu7dLcsmIq/21s+5u7q6vvJYYqa1G8Zjnq7mOI4b7zFFi77iDSIiIiISo0yZMoVly5Y5jg3DcOx3t3LlSiZPnmxhOhEREYnqnJ1h8mSIEyfIsk/r+rIgcR5m5waGDbNvoi0ikc4bb5z91J49e4LdQOzZs8fx65BuLFq0aBHKaBKVLL+4FU6PdxxXreaMy1uPLhERERGJrqZOnRrsOEmSJNy4cSPYed07iIiIyLvInBmGDjVo2zawEOAG86bRsWlRyp/1I1nLlnDwICRObGlOEQnurX+MPGvWLGbNmvVC3TRNPvnkkxfqutGI/sz//mPpw6zgl9BRq1nd2bpAIiIiIhLp7N+/H8MwSJ48OZs2bSJLliycOHGCcuXKcfXqVby1oaWIiIiEgTZtYOlSWLw4sOCTjxs7fuGLGt8ye85laNcOZs8Gw3jl1xGRiBOqbY1N03S8IPhU7ae1p/8r0d+BZeO4eblasFrVqhaFEREREZFI6ebNmwA0b96cLFmyAJAtWzaaN28OwK1btyzLJiIiItGHYcDff0OyZEF+Nrm9C3PcyzM3FzB3LkyZYlk+EXnRWzUpgjYhnq8FratBEbOs3DcXTj1rUuTI/YhUqSwMJCIiIiKRTkBAAADJkycPVk+WLFmw8yIiIiLvKlkyGD8+6EwJJ1g4ifYVE3LdA/jiCzh3zqp4IvKcN17uacOGDeGZQ6Iqm43Ft3zgSmFHqW6t2BYGEhERERERERGRmK5mTfvKTn/9FVi4m5YbG/6gU/WmzJh3z35yxQot+yQSCbxxk8LT0zM8c0gUdW/PVnb4lg9Wq1btJReLiIiISIz322+/8fvvvzuO79696/h1pkyZgl1rGAanT5+OsGwiIiISvQweDOvW2zh1MnAxmUNNmJltKQ1zzKD+qlUweTK0bGltSBF5+42zRYJav2YMtn+fdSXcPR5TsqSrhYlEREREJDK7efOmY3+Kp4zAJxjPnj0bYl1EREQkNOLEgWlTnShZ0iQgIPDfFctG0bblFsqeu0CSzp3tG6umSGFtUJEYLlQbZ4s8tfzfDXDq2S7ZFSs54aoehYiIiIi8hZD2vhMREREJC0WLQs+eQR588EvI9dWT+LyGAbduQceO1oUTEUAzKeQdmHfusORBKnj4nqNWq4aGlIiIiIi8qGzZspoZISIiIpbo0QOWLbexa2fg89pnyzMnZwdm5vmDRvPnw9y58OGH1oYUicH0E2UJtRMrpnLlavANKLQfhYiIiIiExMvLy+oIIiIiEkO5uMD0aU7kzRfAwwfO9uLafrT7dCllz50jVceOUL48JElibVCRGErLPUmordo1HU5VdxxnyvKQ9OktDCQiIiIiIiIiIhKCzJlhQH/nZ4Uncbm7+m9a1wbTxwc6d7YunEgMpyaFhI5psvjaObhU1FGqWzu2hYFERERERERERERerkMHKFXK9qzwb2VW+bZmzPvAlCmwfLll2URiMjUpJFQeHjnAJt/SYD7rQFevrjWGRUREREREREQkcnJygvHjnXCLHaRRsWownUum4nQioF07uHvXsnwiMdU7Nyn8/f35559/WLVqFWfPng2DSBIVbF75F0/OPNuAws3tCWXKWBhIRERERERERETkNbJlg5/7BPmRqF9CHq4aTcu6EHDpInTrZl04kRgq1E2KgIAAfvjhB5IkSUKRIkWoUaMG8+fPZ9GiRVSoUIGKFSty/fr1sMwqkciKEyvh1LMmhWd5k9ha7UlERERERERERCK5zp2hSBHzWeFEHbbea8SQEsBff8HGjZZlE4mJQtWkME2T+vXr07dvX+7du4dpPvtDXapUKTZv3oyXlxeLFy8Os6ASiTx6xKI7CeF+Ckepdk1X6/KIiIiIiIiIiIi8IRcXGD/eIFasII2K5SP5vlhSDiUDPvsMHj2yLJ9ITBOqJsWECRNYunRpiOfee+89SpQoAfDSayRqO7dmLmeuVw1Wq1btJReLiIiIiIiIiIhEMnnywA8/BNlf9eF7PFk9khb14cnpk9Cnj3XhRGKYUDUpJk6cCIC7uzt//vnnC+cLFy6MaZocP378ncJJ5LRq66RgSz2lTfeALFksDCQiIiIiIiIiIvKWvvsO8uYNMpvi8Mfsu12PfqWBAQNg/36roonEKKFqUhw4cADDMGjZsiVt27Z94XyyZMkAuHz58rulk0hpyZWjcL6U47hObXcL04iIiIhIVLV161ZatWpFnjx5SJ06Nblz56Zly5Zs2bLF6mgiIiISA7i6woQJBs7OQRoVy0bRp1gCDrwXAG3agL+/dQFFYohQNSkeBa7Jljp16hDP37lzBwA/P79QxpLI6sm5f1nrWxRMF0etenXjFe8QEREREXnRN998Q9myZZkyZQpHjhzhypUrHDt2jKlTp+Lp6Unnzp2tjigiIiIxwPvvw7ffBvnZlm9K/Df0pVU9eLJvLwwbZlU0kRgjVE2KpzMldu7c+cI50zQde1EkT578HaJJZLR92V88OvtsqadYsZ5Qrpx1eUREREQk6hk9ejRDhw7FNM1g9afHpmkyYsQIRo8ebUU8ERERiWF69YKsWYP8u2TP5+zzL07/0kDPnnD6tGXZRGKCUDUpSpQo4WhGfP7554769u3bqVy5MocPH8YwDEqWLBlmQSVyWHF4EZyq7jguWTqAOHEsDCQiIiIiUc4ff/zh+LWLiwvly5enUaNGlC9fHhcXFwzDwDRNfv/9dwtTioiISEwROzaMHv3cSiFLxvBTaRcOxnsIbdvCcw9XiEjYCVWTImhjYsyYMY6biPnz57Nhw4YQr5NowN+fhTdiwd20jlKdWrEtDCQiIiIiUdGpU6cwDIO0adNy4sQJ1q1bx/Tp01m3bh3Hjx93LCv777//WpxUREREYoqKFaFFiyAFn7z47/zGvuyT13qYONGiZCLRX6iaFOXKlaNbt26YpumYkm0Y9m7j0+Nvv/2W0qVLh1HMtzNgwAAMw8AwDHbs2BHiNXfv3qVLly6kT58eNzc30qdPT5cuXbh7924Ep406rm5ewbFbVYLVqld/ycUiIiIiIi+RNGlSAJo1a0b69OmDncuQIQPNmjULdl1E0/2EiIhIzDRoECRJEmTGxMZe/BM7EwNLAV26wNWrlmUTic5C1aQA6NevH1OnTqVgwYIAjoZF/vz5mTJlCv369QuzkG/j6NGj9OzZkzivWIPo/v37eHp6MnToULJnz07nzp3JlSsXQ4cOxdPTk/v370dg4qhjtddYOPmsK5E8xQNy5LAwkIiIiIhESQ0aNMA0Te7cuRPi+du3bwPQsGHDCExlp/sJERGRmCtpUhg0KMiyT/7usGwUvTzhkOtt6NTJsmwi0VmomxQATZo0Ye/evfj6+nLx4kV8fX3Zt28fTZs2Dat8byUgIICWLVuSP39+6tev/9LrBgwYwP79++nWrRurV6+mX79+rFixgp49e7J//34GDBgQgamjjiUX9sH5Mo7jOnXcMIxXvEFEREREJAR9+vQhT548jB8/nlmzZgXbMHvmzJlMmDCBwoUL89NPP0VoLt1PiIiISMuWUK5ckMLpqvgfacwndcF//lxYsMCqaCLR1js1KZ5yd3cnVapUeHh4hMWXC7X+/fvj7e3N+PHjcXZ2DvEa0zQZO3YscePGpWfPnsHOde/enUSJEjFu3DjHjZLYBfj8x6o7+SDAzVGrUT3k/49FRERERF6lYMGC+Pj48OjRI5o0aYK7uzupU6fG3d2dpk2b8vjxYy5evEjevHnJlCmT45U5c+ZwzaX7CRERETEM+PNPcHUN8nf5qqHsSZSIgSWBDh3g1i3L8olER6FqUkycOJEKFSpQoUIFzp49G+zc+fPnqVixIhUqVGBiBG4oc+jQIX766Sd++OEHcufO/dLrTp48yeXLlylVqtQLU7hjx45N2bJluXTpEqdOnQrvyFHKP8vGcu/Cs6WenJwDqFDBwkAiIiIiEmWdPXuWa9euYRgGpmny+PFjrly5wuPHjx0/3P/vv/84e/YsZ8+e5dy5c45fhxfdT4iIiMhT2bNDjx5Blg+5nxzW9qd3OTjmfxW+/daybCLRkUto3jR16lS8vLwoUaIEGTJkCHYuXbp0AHh5eWGaJq1atXrXjK/l7+9Pq1atyJkzJ999990rrz158iQAWbNmDfH80/rJkydDvMbPzw8/Pz/Hsc1mwzRNbDZbaONHCcv3zYWT8xzHRYv5ETdubKL5bztMxJQxIqGnMSKvovEhr6MxIlHVq2YaPH8uvGcl6H5CIjONEXkdjRF5FY2P0OvWDWbMMDh+PLBZ8c9nPM4/mU/rbGHT+HEYH38MFStaGzIMaIxIZBCqJsWRI0cwDIOSJUuGeL5IkSJs2LCBo0ePvlO4N/Xbb7/h7e3Nzp07iRUr1iuvfbo5X4IECUI8Hz9+/GDXPa9v374vrI2bKFEifHx8cHIKk9WzIh/TZOE1P7idyVGqWD4AHx8fC0NFHTabjTt37mCaZvQdI/JONEbkVTQ+5HU0RuR1/P39cXEJ1T/7w02vXr2sjhCM7ickMtPnvLyOxoi8isbHu/ntt1h88EGSZ4UlY9jWriB/FPWjQ5s23NiwAdPi5e/flcaIvEpE3UuE6jvcuHEDsE9nDvGLBga/FQHrs3l7e/PLL7/QtWtXChUqFO7fr3v37nTp0sVxXKxYMQICAkiWLFm0/YN8a/dmvG9VClb74IM4JEsW5yXvkKBsNhuGYZA0adJoO0bk3WiMyKtofMjraIzI60S2BgVEriaF7ickstPnvLyOxoi8isbHu6lXD1q1Mpk4MXA2xfWcsLEX3Sv2oM6o86QfORJz8GBLM74rjRF5lYi6lwjVd4kbNy63b99m/fr1/Pzzzy+c37Bhg+O68NayZUsyZ85M79693+j6p088vezJprt37wa77nlubm64uT3bPNrJyQmbzYaTk1O0/YO8ft0YzNNNHceJkzygQAEPDOMVb5JgDMOI1mNE3p3GiLyKxoe8jsaISOjpfkKiAn3Oy+tojMiraHy8m8GDYcUK+O+/wMLWb3mQcx6f1d7L6uHDcPr4Yyhe3NKM70pjRKwWqiZF7ty52bJlCzt27KBTp0706tWL9957jxs3bvDTTz+xfft2DMMgV65cYZ33Bd7e3sDLZ3WUKFECgAULFlCvXr1ga8SG5HVrzMZES//dDmfHOo5r1XJVg0JERERE3pmfnx979uzh4sWLwfZpCKpFixbhmkH3EyIiIvIqiRPD6NHQoEFgwXSBRRNY2/Z9JuZ/wieffgr//ANBHkIQkbcTqiZF3bp12bJlCwCjRo1i1KhReHh48ODBg2DX1a9f/90Tvsann34aYn3Tpk2cPHmSOnXqkDRpUscG31mzZiVVqlRs3bqV+/fvEyfOsyWLHj16xKZNm0iVKhVZsmQJ9+xRgXnvHktvZwV/d0etZo3It2SAiIiIiEQtkydP5ptvvuHmzZuvvC68mxS6nxAREZHXqV8fGjaE2bMDCz55YfP3dKnam2p/HCFl377whrMyReRFofppc/v27Rk9ejRnzpwBwDRN7t+/D9inBwFkypSJ9u3bh1HMlxs7dmyI9VatWnHy5Em6d+9O8SBTrgzDoE2bNvTp04c+ffrQv39/x7m+ffty69YtOnXq5Ph9xHSHVkzk5qVqjmPDsFG5sqZ+iYiIiEjobd26lU8++QSw30s8ZRjGC8fhTfcTIiIi8iZ+/x3Wr4fr1wMLm3twO8cCOtT0Zv5vv2J8+CHkyWNpRpGoKlRNijhx4rBy5Urq1avHkSNHgp0zTZPcuXOzcOFCPCLp7vbdunVj8eLFDBgwgH379vH+++/j7e3NihUrKFCgAN26dbM6YqSxcvcMODnecVyg0AMSJQr/vUZEREREJPr6/fffQ2xGPK0936yIbHQ/ISIiEvMkTWpvVDRqFFiwxYJFE1j4WVHmZvXno88+g61bQfs6iLy1UP+pyZIlCwcOHGDRokV069aNNm3a0K1bNxYtWoS3tzeZM2cOy5xhKk6cOHh5edG5c2eOHTvG4MGDOXToEJ07d8bLyyvYlO2YbsGlm3Ajh+O4Xh39fyMiIiIi72bHjh0YhkHRokX55ZdfHA2J+/fvM3bsWJycnGjVqhVPnjyxOGnIdD8hIiISMzVsCPXqBSlcLQhbu/FFDbjhvQPGjbMqmkiUZpiR+RGlKCB37tz4+/tz9OhRnKJZp9T3qDcJuo7GtvxPR233bihc2MJQUZDNZsPHx4dkyZJFuzEiYUNjRF5F40NeR2NEXid37twAHD582OIkz3h4eODn58d3331H4sSJ+fbbbzEMg4CAAMC+D8W0adPo27dvtJ+VEJ3vJyRs6HNeXkdjRF5F4yPsXb0KuXLBrVuBBWc/aFeIZleOMMUrERw/bp92EUVojMirRNS9hEaevNSGFaOwna7uOI4f/wGFClkYSERERESilQQJEuDq6uo4vnPnDgDZsmXDNE3G6WlEERERiWRSpIDhw4MUAtxg4QSm5nFmRZJbEM0fsBAJD6FuUqxdu5bq1auTNGlSXFxccHZ2fuHl4hKqLS8kklh2Yj38W9FxXK2Gi5bVExEREZF3ljhxYsC+vFOSJEkc9fHjx3P79m2WLVsGwPnz5y3JJyIiIvIqzZpBjRpBCpeLwo7OtKsN96ZPhM2brYomEiWF6kfO8+fPp1q1aqxevZobN25gs9kwTTPEl0RN5qNHLLqeFp482yS7Ti3XV7xDREREROTNpE6dGoAbN26QN29eR71r164kSZKEXbt2AQRrYIiIiIhEFoYBf/0F8eMHKW74iQsBGeleCfj8c4ike2uJREahalL06dMHm80W1lkkEjm1bjZXr1RzHBuGjSpVLAwkIiIiItFGoUKFME2T/fv3kzdvXnLlyuU49/RBJ8Mw+Pjjj62KKCIiIvJKadLAoEFBCv4esGw0fxSGLfcOw9ChlmUTiWpCtR7T8ePHMQwDZ2dnvvjiC7Jnz46bmxuGYYR1PrHIyq2T4dQQx3GuPL4kTRr/Fe8QEREREXkzbdq0IUuWLMSOHRuAyZMnU6tWLa5evQrYGxXVqlXj119/tTKmiIiIyCt9+ilMnQqbNgUWTleFQ41pU2cG+3/pTexGjSBdOkszikQFoWpSJEiQgGvXrtG+fXuGDBny+jdIlLP4wiXwyec4rl83noVpRERERCQ6ef/993n//fcdx4UKFeLff/9l06ZN3Lhxg+zZs1OoUCELE4qIiIi8npOTfdmn/Pnh8ePA4sphHP9iJT8XucWvX30FCxZYmlEkKgjVck81a9bENE2ctItytGS7d5et10sGq1WvrlkyIiIiIhJ+YseOTZUqVWjcuLEaFCIiIhJl5MgBPXoEKTxIBmsGMqAU7N+xEJYutSqaSJQRqpkUv/zyCytWrGDcuHFUq1aNatWqvf5NEmUcWjeDhxeebUDh4XGfokXjWJhIRERERKKy8+fPh/q96bREgoiIiERy330HM2fCsWOBhX2f4p9vCp/W2cjOL7/ApUIF8PCwNKNIZBaqJkXTpk2JEycOV69epWbNmqROnZr06dMTK1asYNcZhsG6devCJKhEnI3/LISzExzHpcs8wsVFTQoRERERCZ0MGTKEav86wzDw9/cPh0QiIiIiYcfNzb7sk6dnkOLSv/infX6GpDpHt169YOBAy/KJRHahalJ4eXlhGAaGYWCaJhcvXuTSpUvBrjFNUxtpR1HLT92G+ykcxzVrJLYujIiIiIhEG6ZpWh1BREREJFyULQtt2sDYsYGFG9lhS3d6letNvb8Gk+2DD6B4cUszikRWod5UwjTNYDcZT4+fr0vUYvr5sfVGnmA1T081m0RERETk3egeQURERKK7AQMgefIghc3deXQrJ21rmdhatYSHDy3LJhKZhWomRcuWLcM6h0QSJzct4N6VZ3PTPDx8yZs3roWJRERERCSq27Bhg9URRERERMJdokQwbBg0bhxYsLnC0r/Y2MqT8QdO0KZnTy37JBKCUDUpJkyY8PqLJErauHMOnBvmOC5a5AFOTmpSiIiIiEjoeQZboFlEREQk+vr4Y5g8GVasCCycLwP/fEbXKmOo+ccgUjZoACVKWJpRJLIJ9XJPEj0tP3kZ7qZ1HNeo9Z6FaUREREQkuvH19cXZ2RlnZ2fq169vdRwRERGRMGUYMGoUeHgEKa4ZwJ3HqehUHWjVSss+iTwnVDMpgrp06RIXL17Ez88vxPNly5Z9128hEcVmY/N/OYKVypdTH0tEREREwk7cuHGJGzcuvr6+FC5c2Oo4IiIiImEuQwbo0we6dg0s+CWAZX8wr1F9Fh44QT0t+yQSTKibFOvXr+fLL7/k6NGjL73GMAz8/f1D+y0kgp3btZob/5VxHLu5PaBAAY9XvENERERE5O0VKFCALVu2cPnyZaujiIiIiISLr76CmTNhz57AwvF6cOQDOtaYR/nfB5FAyz6JOITqMflDhw5Ro0YNjh49immar3xJ1LF56ww492y94IIF7uDyznNtRERERESC69WrF05OTkybNo0jR45YHUdEREQkzLm4wLhxBP/Z2vLfueySiO8qomWfRIII1Y+ghw4dyuPHjx3HhmEAOJoShmGoQREFLT9yCm5ldhzXrJ3UwjQiIiIiEl1t2bKFIkWKsGPHDgoWLEjNmjXJli0bHh4vzuLt2bOnBQlFRERE3l2+fPC//8GvvwYW7qeA1YP4s96nNDl4gjI//giDBlmaUSQyCFWTYvPmzRiGQc6cOalWrRpDhgzBMAwOHz7MjBkz+OWXX2jVqhW9evUK67wSXkwTr8uZgpUqlNc0ChEREREJe71798YwDAzD4MmTJyxatOil16pJISIiIlHZDz/A3Llw/HhgYX9ryDudz+qsY//wwcT+8EMoXtzSjCJWC9VyT5cuXQKgZs2apEyZ0lHPmTMnffr0oW7dukyaNIl169aFTUoJd/8d3sWVa6Ucx7FiPUL7GIqIiIhIeAs6K1tLx4qIiEh0Ezs2/P33c8UlYzge34NfywCffw7a01diuFA1KQICAgBIkiQJsWLFctTv378PQOHChTFNk+HDh4dBRIkImzdODrYfRd7cN3B1tTCQiIiIiERb6dKle+GVPn36F17p0qWzOqqIiIjIOytTxt6LcLidCTb8RL/ScPDyfhg1yqpoIpFCqNbzSZQoET4+Pjx8+JAUKVI46kuXLuXjjz9m8+bNAJw8eTJsUkq4W3n4AFzP6TiuXvM9C9OIiIiISHR29uxZqyOIiIiIRKh+/WDJErh4MbCwozP+eWbRrvYetvz4A04ffQRBVqwRiUlCNZPi6RJPN2/eJGfOZz/Ybtq0KUmSJGHNmjUAxI0bNwwiSkRYezZ1sONKldwsSiIiIiIiIiIiIhK9xI8Po0cHKZjOsHgs21O58Fe2e9C1q2XZRKwWqiZFwYIFMU2Tw4cPU7hwYdKmTQuAzWbj1q1bmKaJYRjUrl07TMNK+Lh17hjnbpR0HDs7P6ZYMQsDiYiIiEiMsWPHDoYMGUL37t0dM7JFREREoqNataBRoyCF//LD9s58VwmuLJ4O69dblk3ESqFa7qlJkyZ4eHgQN25cDMNg7Nix1K9fnwcPHjiuyZMnDwMHDgyzoBJ+tq6fBOeefULmyOaDu3saCxOJiIiISHR38eJFGjduzLZt2xy1pEmTcu7cOVq2bIlhGBw9epSsWbNamFJEREQkbA0fDqtXw82bgYWNvbibdwZfVb/I7A4d4MABtFGsxDShmklRsWJFRo4cSd++fQGoXLkyJ0+e5M8//+TXX39l7ty57N27l8SJE4dpWAkfq/bvgP/yOo6rV09kYRoRERERie7u3r1L+fLlHQ0K0zQd5z766CM8PDwwTZMFCxZYFVFEREQkXCRLBv37Byk8iQMrhzEnNyyzHYfBgy3LJmKVUDUpzp8/z/nz5/H19XXUUqZMSdu2benevTsNGjTAxSVUkzTEAqv/fY+gQ6Fy1TjWhRERERGRaG/IkCGcPn0aCN6gAHBzc6NcuXKYpsnq1autiCciIiISrlq3huLFgxSOfgAnq9KhJvj26wNnz1oVTcQSoWpSZMiQgYwZMzJmzJgQz0+aNIl8+fKRP3/+dwon4c/32iVOXn/2qWgY/pQs+Yo3iIiIiIi8o/nz5wOQOnVqjh079sL5vHnts3zPnTsXoblEREREIoKTE4waZf9fh+W/cz6OG72LP4KvvrIsm4gVQtWkeJ3r169z6NAhDh06FB5fXsLQjnWTMM+XdRxnyXSFuHEtDCQiIiIi0d7p06cxDIPmzZuTLVu2F87Hjx8fgMuXL0d0NBEREZEIUbAgdOgQpHArC2ztxrDisG/XYli82LJsIhEt3JoUEjWs+ccLrhRyHFetEs+6MCIiIiISo7i5uYVYf9qccHZ2jsg4IiIidpm8MwAAg2tJREFUIhHq558hefIghS3dCbidkba1IeCrTvDggWXZRCLSG28c0bp16xdqs2bNemG2xIMHD1i2bBkAsWLFesd4Et5WnIgL5rObv6o1EloXRkRERERihHTp0nH8+HEWLlxIjx49gp3z8fFh9uzZGIZB+vTpLUooIiIiEv4SJoRBg6B588CCvzusGMGeJrX5Pfl5vvr1V/j1VysjikSIN25STJw4EcMwHMemabJnzx727NnzwrWmaWIYBlmyZAmblBIu/HzvcORakSAVG6VLh8vkGhERERERhwoVKnD8+HG8vb0pXLiwoz516lQGDhyIj48PhmFQqVIlC1OKiIiIhL+mTWHsWNi4MbBwshYcr8MPFRbT4M8BpG3eHHLksDSjSHh7659Im6YZ7NchvZ6e69ixY9gllTC3e8NUAi48248iQ9rLJExoXR4RERERiRm6dOmCu7s7AAcPHnQ8DOXt7c1///0HgLu7O19++aVlGUVEREQigmHAH3+AS9BHyVcMx9dw58vK/tCxIwT5eaxIdPTGMynKli3ruHnYuHEjhmGQMWNG0qZNG+y6WLFikTp1aho0aEDt2rXDNq2EqbW7VsKlzxzHFSu4WphGRERERGKKzJkzM2XKFJo1a8ajR48AMAzD8cCTm5sbkydPJmPGjFbGFBEREYkQuXPD11/bl34C4E4G2Pw9Cyv+wKL966k7cyY0bmxhQpHw9cZNCi8vL8evnZzsEzA6dOhAly5dwjyURIxlR13A9qwxUaNOMgvTiIiIiEhM0qBBAw4fPszvv//O1q1buXnzJokSJaJkyZJ06tSJTJkyWR1RREREJML06gUzZsClS4GFrd9C/sl8UeMEFf739f/bu++wKI4+DuDfA46jKUVABAUUQQXF3kGw915jxd67MdGoYDcao8bEEoPlxd5bjB3U2Ilo7KIiFiQIIigCArfvH8rKSceDpXw/z8PzOLOzu7/dG3DnZmcGxdq0AQwNJY2RKLdkuZMipVmzZkEmk6FBgwbqjofySGJCPG78V00lz9VVmliIiIiIqGgqW7Ysli5dKnUYRERERJIzMACWLQN69PiUodQGjvyK5/1awNMxDD97egLLl0sZIlGuydEqyV5eXvD09ES9evVU8oOCgrBv3z74+vpCqVSqJUDKHdfP7ULC88/rUVhavICZmYQBEREREVGRUbVqVfzyyy+IiIiQOhQiIiKifKNbN6B58xQZj5sDt3tgRV0gYNcvwPXrUoVGlKty1Elx/Phx9O7dG3369MGzZ88AAMuXL4eDgwO6deuGZs2awcXFBe/fv1drsKQ+py/uB55/7mRqzFEURERERJRHbt68iYkTJ8LKygrdunXDn3/+yZeciIiIqMiTyYBffwW0Uy4be3QZlB+KYVhbAUkjRwB8ZqJCKEedFLt378b27dtx7NgxlC5dGhEREfj++++RlJQEQRAgCAIuX76MFStWqDteUpNDNxKBRF0x3bZTKQmjISIiIqKi6MOHD9i3bx86dOiAMmXKYNq0abh//77UYRERERFJxsEB+O67FBnvLAG/2fC3AlYrLwPr10sWG1FuyVEnxdWrVyGTyeDu7g6ZTIaTJ0/iw4cPkMlkMPy0gIsgCNi/f786YyU1USqT8M9/lVXy3BvnqCoQEREREWXb2LFjYWFhAQDiS06hoaFYvHgxHB0d0bBhQ6xfvx7v3r2TOFIiIiKivDdtGlC2bIqMy2OBUGdMbwq8mPMtEB4uWWxEuSFH30yHhIQA+LjQHQD8+++/AABXV1dERkaiZcuWAIAHDx6oI0ZSszv+fyE2xXoU5qYvUYoDKYiIiIgoj6xYsQLPnz/HqVOnMGTIEBgbG4udFYIg4NKlSxg6dChK8SGViIiIiiBdXWDlyhQZghbw5yq8lcswvv6bL4ZaEBV8OeqkiIyMBACYmpoC+NgZIZPJ0KBBAwAfOysAICYmRh0xkpr5/r0LeNZATLvWjZcwGiIiIiIqimQyGRo3bozff/8d//33Hw4ePIhvvvkG+vr6AD6OsOAad0RERFRUtW0LdOqUIuNZQ+C6B/Y4Aof/Xg+cPy9VaERql6NOCh0dHQBAYGAgACAgIAAAUL58eQBAbGwsAIhTP1H+cvBaNJBgIKY7dCsjYTREREREVNRpaWmhRIkSMDExgZ6entThEBEREeULy5cDKo9GJxYD700wug0QM3oYkJAgVWhEapWjTgp7e3sIgoBNmzahcuXKePz4MQCgWrVqAD5PB5U8zyzlH4Ig4PJzB5U8t8aaEkVDREREREXZzZs3MX36dJQrVw4uLi5YtWoVwlPMsayhwXXTiIiIqOiysQFmzUqREWsKnFqAp0aAl/kdYMUKqUIjUqscPfV37NgRAKBUKnH37l0AgLW1NWrUqAEAuHLlCmQyGapWraqmMEldHt09j7chrmLa2Og/2NhIGBARERERFTkLFixAlSpVUK1aNfz4448IDg5WWZOifPnymD9/PoKDg6UOlYiIiEhSEycClSqlyPhnKPCsLpbVB278OhN4+lSy2IjUJUedFFOmTIGbm5vYiChRogQ2bNgAALh37x5u374NQRDQsGFDtQZLX8/vzGbgqYuYblDjrYTREBEREVFRNGPGDNy5c0elY8LAwACDBg3CuXPncP/+fUybNg2WlpZSh0pEREQkKW1t4LffUuZoAH+uRpKgieHN45A0bqxUoRGpjVZOdtLT04Ovry/u3buHuLg4VKpUCQqFAgBQtmxZvHz5EgBgbGysvkhJLQ5c+Q+INxLTHbkeBRERERFJQBAEyGQyNGrUCAMHDkS3bt24HgURERFRGho3Bvr0AbZs+ZQRWh24PB6XG/yMtTcOYtSBA8CnmW+ICqIcdVIkq1ixYqo8hUKBkiVLfs1hKRddeFpWJe3eTCFRJERERERUVFlbW2PAgAHw8PBA2bJlM9+BiIiIqIj76Sfg8GEgKupThu8coNJeTGv6BJ2+GwXLZs0AfX1JYyTKqSx1Ujz9NLeZiYkJDAwMxHRWWFtb5ywyUrtnT2/idcjnqZ6KGYSjfHlTCSMiIiIioqIoKCgIMplM6jCIiIiICgwLC2DJEmDYsE8ZCfrA4TWI7tsKE5xDsHPOHODHHyWNkSinstRJYWtrC5lMhp9++gkTJ04U05mRyWRITEz86iBJPc76bgKCvxfTdZwjIJOxk4KIiIiI8pZMJsO5c+dw7tw5carYUqVKwdXVFa6urhJHR0RERJQ/DR4MbN4MnD37KeNRS+DfPthVdQv+3P4T2vbtC1SpImmMRDmRremeBEHIME3524ELQUDs506Jjl2sJIyGiIiIiIqiixcvYvjw4bh9+3aa252cnPD777+jXr16eRwZERERUf6moQGsXQtUrQp8+PAp89gyoPxRjG4VAffRw6Dvd/5jQaICJMs1lh0UBd/ZR6qLZDdpZSBRJERERERUFJ04cQJNmjTB7du302xPCIKAW7duoXHjxjh16pQEERIRERHlbxUrAjNmpMh4bwYcX4pgI2C2ziVgwwapQiPKsSyNpPD19QUA2NnZqaSp4Ah79QT/hX5+G01PNxKOjsYSRkRERERERUl0dDT69OmD+Ph4cerYtDoqZDIZ4uPj0bt3bzx8+BDFihXL61CJiIiI8rXvvgO2bwfu3PmUcWMA4LwZP9c/iT4/TkTVdu2AkiUljZEoO7LUSeHm5pZhmvK/c76bgOBhYrqm00vIZOykICIiIqK8sW7dOoSHh0Mmk0FDQwO9evVC9+7dYWNjAwAIDg7Gjh07sHPnTiiVSoSHh2PdunWYNGmSxJETERER5S/a2sC6dYCLCyC+83F4DZJGVsEwt7e4MGEcNLftkDRGouz46gnKIiIiEBERoY5YKBcd/PsW8K6UmG7f3kLCaIiIiIioqDly5AgAQFNTEydPnoSPjw86dOiAqlWromrVqujQoQO2bNmCEydOQFNTEwDw559/ShkyERERUb7VoAEwcmSKjEg74IwnrpQGVj3eCRw+LFlsRNmVo06KkydPok2bNtDX14e5uTnMzc2hr6+Ptm3bcu7YfMr3vuoQr+YdTCSKhIiIiIiKotu3b0Mmk6F79+4Zjsx2d3dHt27dIAhCuotrExERERGwYAFgaZki48Jk4GU1TG8KPJsyFHj7VrLYiLIjW50UgiBg3LhxaNmyJY4dO4bY2FgIggBBEBAbG4ujR4+iRYsWGDduXG7FSzkQ9TYcz0LriGmF4i2qVJEwICIiIiIqciIjIwEAlStXzrRslU8Pq8n7EBEREVFqhobAb7+lyBC0gIPr8E5LE6NqhEKYPk2y2IiyI1udFAsXLsSvv/4qLnCXvOBdyn8LgoDffvsNCxcuVGOY9DXOn/EBgj+/rVbV4Rk+jaAnIiIiIsoTenp6AJClqWLDw8MBALq6urkaExEREVFB16kT0KVLioyXtYArY3G4ArDL9zfgwgWpQiPKsix3UoSEhMDLywsymQwymQyCIKBq1aro0aMHunfvDmdnZwiCIG7z8vLCy5cvczN2yqJDZ64AUTZiul0rLphNRERERHmrTJkyEAQBO3fuRExMTLrl3r17h507d0Imk6FMmTJ5GCERERFRwbRyJVC8eIqM0/OASBuMaw1EjhwIxMdLFhtRVmS5k2LDhg1ITEwEAJQrVw7+/v64du0atm/fjh07diAgIABXrlxBuXLlAACJiYnw9vbOnagpW07eNlJJt+zKRbOJiIiIKG+5uroCAF68eIGmTZvi7Nmzqcr4+fmhadOmePHiBQBkuHYFEREREX1kaQn8+GOKjAR94M/V+E8f+Nb2AbBokWSxEWVFljspkhsRWlpa+PPPP1GjRo1UZWrVqoXDhw9DS0tLZR+Szvv4d3j0sqaYlstjUL2GLIM9iIiIiIjUb/jw4eK/r1y5gsaNG8PMzAw1a9ZErVq1YGZmhqZNm8Lf318sN2zYMClCJSIiIipwhg0DGjZMkfGwNXCrF7xrAH4+c4E7dySLjSgzWe6kePToEWQyGVq2bAkHB4d0y1WoUAGtWrWCIAh4/PixWoKknLt0cSeEp5/fQHMq9wRyuYQBEREREVGR5OzsjLFjx6pMERsREYGAgABcu3YNERERKmvfjRkzBs7OzhJHTURERFQwaGgAv/8O1e/9/loBvDfBsDZJiB0+CFAqJYuPKCNZ7qRIXryuevXqmZZNLpO8D0nn0MnTwGt7Md26qb6E0RARERFRUfbzzz9j+PDhKp0RKX8AQBAEDBs2DMuWLZMyVCIiIqICx9ERmD49RcZ7c+D4EgSWAOZpXwZWr5YsNqKMZLmTIjY2FgBgaGiYadnin1Zqef/+fQ7DInX564aeSrpNT2uJIiEiIiKiok5TUxOrV6/GhQsXMGDAAJQtWxY6OjrQ0dFB2bJlMWDAAJw/fx6rV6+GhkaWmypERERE9Mm0aUDFiikyrg8CHjfG4obAvz99CwQFSRYbUXq0slowISEBMpkM/v7++N///pdh2eR5ZJOSkr4uOvoq7z/EIPBFLTGtqRmH2nV1JIyIiIiIiAioV68e6tWrJ3UYRERERIWOQgGsWwe4uqbIPLwWiSOdMbRZLC4MHgTNk6c+zg9FlE9kuZMi2Y4dO7Bjx47ciIXU7OLf26AMbiymHcsFQaGoJGFERERERERERERElJtcXIDhw4G1az9lvLYHzszElWY/4Nebfhi/di0wcqSUIRKpyFGXmSAIGf5Q/nDwpJ/KehTtWuilX5iIiIiIiIiIiIgKhUWLgFKlUmRc+BYIrYIfmgJP5k7mtE+Ur2SrkyKrnRDsqMgfjl4vppJu35vrURARERERERERERV2RkbAypUpMpRy4MB6xGhqYUTTWAiDBwFKpVThEanI8nRPvr6+uRkHqdn7+HcIDKktpuXyGNSqrS9hRERERERERERERJRXunQBOnYEDhz4lPGyFvD39zjmNg9bb/ihz9q1H+eFIpJYljsp3NzccjMOUrMLZzZDCG4ppiuXfwK53EnCiIiIiIiIiIiIiCivyGTAqlXA2bNAZOSnzDOzAIdDGN/6BlrMmoISLVoA+nyxmaTFZdwLqf0n/wbelBXT7VsVy6A0ERERERERERERFTaWlmlM+7R/EyK05Zjk+h6yoUM57RNJjp0UhdSxf41U0u16lZEmECIiIiIiIiIiIpJM795A584pMv6rCpyZhc1VgWPPfKHr4yNZbEQAOykKpZjYaDwKqSOmtbXfonoNmYQRERERERERERERkRRkMmDNGsDUNEXm398DL2phRDtAtnA2EBQkWXxE7KQohM77+UB40kRMOzs8gVaWVx8hIiIiIiIiIiKiwsTc/GNHhUjQAvZtQrC+Ap71YiEbPhwQBMnio6KNnRSF0L4Tl4G3pcV0x7bGEkZDREREREREREREUuvaFfjmmxQZ4Y6A71ysqAdcuXcKWLdOstioaCvwnRQvXrzA8uXL0aJFC1hbW0NbWxsWFhbo2rUrLl++nOY+0dHRmDRpEmxsbKBQKGBjY4NJkyYhOjo6j6PPHcdvlVBJt+1ZOp2SRERERERFG9sTREREVJT8+itgYZEi48JkCM8aYHBHIP67ycCzZ5LFRkVXge+kWLlyJSZOnIjHjx+jefPmmDx5MlxcXHDgwAE0aNAAO3fuVCkfExMDNzc3LFu2DBUqVMDEiRPh6OiIZcuWwc3NDTExMRJdiXrEvI9CUIr1KBSKKFStKmFARERERET5GNsTREREVJSYmHw5YEID2L8Rt430ML/6O2DYME77RHmuwHdS1KlTB2fPnsXDhw/h7e2NhQsXYvfu3fD19YWmpiZGjhyJ+Ph4sfzixYtx/fp1TJ06FcePH8eiRYvw119/YdasWbh+/ToWL14s4dV8vb9Pb4LwpLGYrl7xCTQK/KdMRERERJQ72J4gIiKioqZdO8DDI0XGa3vg1HwsdAFuBBwF/vc/qUKjIqrAf33dpUsXuLq6psp3dXVF48aN8fr1a9y8eRMAIAgC/vjjDxgYGGDWrFkq5adNmwZjY2N4e3tDKMC9hXuO/wPEfB6z1am9mYTREBERERHlb2xPEBERUVG0fDlQOuUM8ZfHITGkLgZ2AhImjQdCQiSKjIqiAt9JkRG5XA4A0NLSAgAEBgYiJCQEDRs2hL6+vkpZHR0dNGrUCC9evMDDhw/zPFZ1OXnbVCXdtqelRJEQERERERVsRbE9QUREREWDoWEa0z4d9EaAmTZ+cowCRo7ktE+UZwptJ8XTp09x8uRJWFhYoEqVKgA+NioAwN7ePs19kvOTyxU07969xpOX9cS0rm4EnJwkDIiIiIiIqIAqiu0JIiIiKlpatQL69k3REfHKCTg3DV7uwN0LB4Ht2yWLjYoWLakDyA0JCQno168f4uPjsXjxYmhqagIAoqKiAACGhoZp7le8eHGVcmmJj49XmZNWqVRCEAQolUp1hZ9jZ45vgBDcX0xXr/QMgmDMTk+J5ac6QvkT6whlhPWDMsM6QqR+RbU9QfkT6whlhnWEMsL6QZn56Scl/vpLhoiIj887ODcdHxz3YHDHWzg7bgw03N2BkiUljZEKv0LXSaFUKjFo0CCcPXsWQ4cORb9+/dR6/IULF2L27NkqecbGxggLC4OGxCtU7zh6DXg/WUw3b6aPsLAwCSMi4GOdjIqKgiAIktcRyp9YRygjrB+UGdYRykxiYqI4XRFlrii3Jyh/4t95ygzrCGWE9YMyo1QqMX26EpMnf1qgQqkNHPDGxSH18Wv51xgxbBjeqM4LRUVIXrUlClVrRRAEDB06FJs3b0bfvn2xZs0ale3Jbzyl92ZTdHS0Srm0TJs2DZMmTRLTdevWRVJSEszNzSX/Y3820Eol3WOAHczNJQqGREqlEjKZDGZmZpLXEcqfWEcoI6wflBnWEcoMOyiyrqi3Jyh/4t95ygzrCGWE9YMyo1Qq0avXK/j5KXHo0Kc6ElIHuDQe05suQ/tVh1H2/Hmgc2dpAyVJ5FVbotC0WJRKJYYMGYINGzbgm2++wcaNG1P98c1sjtjM5pgFAIVCAYVCIaY1NDSgVCqhoaEh6R/7t9HhCH7ZQEzr679CpUpmkMkkC4lSkMlkktcRyt9YRygjrB+UGdYRoq9X1NsTlL/x7zxlhnWEMsL6QZnR0JDht9+AM2eAT+9cAKfnIbbiAQzt8BgnJ4yHRosWQLFiksZJhVeh6KRI2aDo2bMnfHx8xHljU7K3t4elpSXOnz+PmJgY6Ovri9vi4uJw9uxZWFpaonz58nkZvlqcPbYBCB4ipms6PYdMZiZhREREVBQlJSXhw4cPUodRpCiVSiQkJCA2NpYNzyJGW1s7zWdeyj62J4iIiKios7ICfvoJGDbsU0aiHnDod/j2b4a1Fi8w0ssLWLpUyhCpECvwnRRKpRKDBw/Gxo0b0b17d2zevDndxppMJsOQIUMwZ84czJkzBz/++KO4beHChYiMjMTYsWMhK4DDD3advAvEGYvprl1KSRgNEREVNYIg4MWLF3j9+rXUoRQ5giBAEAS8fv26QD7D0NcxMTGBlZUVP/uvwPYEERER0UdDhgBbtwJ+fp8ygpoCAYMwtfl6tFm7HDb9+gHVqkkYIRVWBb6TYs6cOdi4cSMMDAzg4OCAefPmpSrTqVMnVPv0CzR16lQcPHgQixcvRkBAAGrWrIkbN27gr7/+QrVq1TB16tQ8vgL18L1noZJu280inZJERETql9xBYWFhAX19fb7Rn4cEQRCniuEXo0WHUqlETEwMQkNDAQClS5eWOKKCi+0JIiIioo9kMmDdOqBKFSAu7lPmsaV4V/4ohrQNwfERwyG7cBFge4/UrMB3Ujx58gQA8O7dO8yfPz/NMra2tmKjQl9fH35+fpg9ezZ2794NPz8/WFhYYOLEifD09FQZsl1QvH0Thqcp1qMoVuwlypXjSAoiIsobSUlJYgeFubm51OEUOeykKLqSn1tDQ0NRqlQpTv2UQ2xPEBEREX1Wvjwwdy7w7befMuKNgH3/w8l+LfDH7SsYum4dMHy4lCFSISQTBEGQOoiCzMnJCYmJibh7965kb40e3r4I7QeMAj4UBwA0aXANp87XkCQWSk2pVCIsLAzm5uZ8s5jSxDpCGSkI9SM2NhaBgYGws7Pjl3MSYCdF0RYTE4NHjx7B3t4eurq6aZZxcnICANy+fTsvQ6Msyg/tCcrfCsKzAEmLdYQywvpBmUmrjiQmAg0aAFevpijY7DsUq70YtzYXh/XVB0DJktIETHkqr9oS/OtUCOw8ESh2UABA124c7k9ERHmPjR6ivMffOyIiIiJSNy0tYMsWQOUdtNPz8Da8Foa5RUOYPEmy2KhwYqumEPB9YKWSbtuFU20QERERERERERFRztjbA7/+miJDKQf2bMWxMgbYcGcrcOqUZLFR4cNOigIuOiIEz1OsR2Fo+Bw2NhIGRERERERERERERAXegAFAr14pMl7bA3/9gkktgeeThqRYXZvo67CTooDz+2sD8NRFTNep+p+E0RARERU+O3bsgJOTE3R1dSGTyXD9+vVs7T9jxgy0a9cOVlZWkMlk8PDwSLfs48eP0aVLFxgZGcHAwADNmzfHtWvXUpWLjo7GDz/8AAcHB+jr68Pa2ho9evRINU/o27dvMXXqVLRo0QJmZmaQyWTw8vLKVvy5yd3dHTKZLN2f0NBQsezhw4fRv39/VKlSBXK5PFvrb/j5+akc19/fP0cxxMfHY8mSJahcuTL09fVRsmRJtG7dGhcuXMh2DF/+jBgxQiy7f//+dOMlIiIiIsorMhmwejVUX4i+PhBRgT0xvPITCD/+KFlsVLiwk6KA23EyCEgwENPdunMYBRERkbq8evUK/fr1g52dHY4ePYqLFy/CwcEhW8dYtmwZIiIi0KFDB2hra2d4LldXVzx48ADr16/Hzp07ERcXB3d3d9y/f1+lbPv27bF8+XIMHToUhw8fxoIFC3D9+nXUr18fwcHBYrmIiAj8/vvviI+PR6dOnbIVd15YtWoVLl68qPJz6tQpyOVy1KtXDxYWFmLZffv24dKlS3B0dETVqlVzdL7ffvsNFy9eRKVKlXIUw9ChQ/H999+jU6dOOHToEH777Te8evUKbm5uuHLlSobnrlGjRqrzXLx4Ef379wcAdO7cWSzr5uaGixcvYsaMGTm6TiIiIiIidTEy+rg+hYaG8Dnz8FocMbPB/w7PA+7elSw2Kjy0pA6Avs6ZB9Yq6badTSWKhIiIqPB58OABEhIS0LdvX7i5ueXoGG/fvhUXN/bx8Um33JIlS/Dq1StcuHABNp9eVXJxcYGdnR1mzZqFHTt2AAAePnyIs2fPYsaMGfj2228hCAKUSiXs7e3RsGFD7N27FxMnTgQA2NjYIDIyEjKZDOHh4fjjjz9ydA25xdHRMVXepk2bkJCQgCFDhqjkr1u3TryPY8aMwT///JOj89WrVy9HMcTHx2Pr1q3o3bs35s2bJ+Y3bNgQlpaW2LJlC+rUqZPuuYsXL57q3IIgoE+fPrCxsUHz5s3FfGNjY9SrVw/37t3L9jUSEREREalbw4bArFkyiIOy4w2BvVswvpcbmg3rBavT/oBcLmWIVMBxJEUBFvXqGV68bCimjU2CYWWVwQ5ERESUZR4eHnBx+TilYs+ePSGTyeDu7i5u9/f3R4cOHWBiYgIdHR1Ur14dO3fuTHWc5C/WM7Nv3z40adJE7KAAPn6x3aVLFxw6dAiJiYkAAPmnh39DQ0OV/Y2MjAAAOjo6Yl7ydEHqEhgYiN69e8Pc3BwKhQKVKlXCb7/9plImeVqjzZs3Y9KkSbCwsICuri7c3NwQEBCQ6Tm8vb1hYGCAnj17quRn9T6qQ1oxaGhoQENDI9V9L168ODQ0NFTue1b5+vri8ePHGDhwYJ5eHxERERFRdv3ww8fOCtGzhoi6MgODbP+FsGC+ZHFR4cCRFAWY75GNwPMpYrpetVcAON0TERHlA1FRwM2bUkeRWpUqwBdfMqdn5syZqFOnDkaPHo0FCxagcePGKF68OICPXy63atUKdevWxZo1a2BoaIjt27ejZ8+eeP/+fYbrTqQlNjYWjx49UpnyJ5mzszNiY2Px+PFjODg4wMbGBh07dsSyZctQs2ZN1KpVC0+fPsWECRNgbW2NXior26nPnTt30KBBA1hbW2Pp0qWwsLDAsWPHMG7cOISHh8PT01Ol/PTp01GjRg388ccfiIqKgpeXF9zd3REQEIBy5cqleY7AwECcO3cOQ4YMgYGBQZplclt6McjlcowaNQre3t5o1qwZmjRpgtevX2P69OkwNDTE0KFDs30ub29vaGhoYODAgeq8BCIiIiIitdPS+jjtU9WqAqKiPr0IdXYmjg86hjVH5mJk23ZArVrSBkkFFjspCrDtJ54DibpiunuvshJGQ0RElMLNm4Crq9RRpHbuHPBpdERm7OzsxKmA7O3tVabqGTVqFJycnHD69GloaX18nGrZsiXCw8Mxffp09O/fP1tvxkdGRkIQBJiYmKTalpwXEREh5u3atQujR49GkyZNxDxnZ2ecOXMGxsbGWT5vdkyaNAnFihXD33//LXbWNG/eHPHx8Vi0aBHGjRuncm4zMzPs27dPHMnh4uICe3t7LFy4EOvWrUvzHN7e3gCAwYMH58o1ZEVGMSxbtgyGhobo2rUrlEolAMDa2hqnT59G+fLls3WeN2/eYO/evWjevDmsra0z34GIiIiISGI2NsDvv8sgDjgWNIF9mzB5cDU0G9UL9mduArq6GR6DKC0cV16AnX2o2qBt06GERJEQEREVHQ8fPsS9e/fQp08fAEBiYqL406ZNG7x8+TLVQtdZldHUTCm3jRw5Env27MGyZcvg5+eHbdu2QVtbG02aNFFZOFtd4uLicOrUKXTu3Bl6enqprjkuLg6XLl1S2ad3794qMdvY2KBBgwbw9fVN8xyJiYnYtGkTnJycUq3dkFcyi2H+/Pn46aef4OXlBV9fXxw4cAAVKlRA8+bNszSVVUpbtmxBXFxcqrU3iIiIiIjysx49gH79UmS8dkDs2YUY4PwISTOmSxYXFWzspCigov4LxsuXn98ELWH6GCVLShgQERFREfHff/8BAKZMmQK5XK7yM2rUKABAeHh4to5pbGwMmUymMloi2evXrwF8HlFx9OhReHt7Y+3atZgwYQIaNWqE7t274/jx43j9+jW8xNXs1CciIgKJiYlYuXJlqmtu06YNgNTXbGFhkeo4FhYWaV4jABw5cgShoaGSfmmfUQx3797FrFmzMHv2bMycORPu7u7o0KED/vzzTxgZGWHSpEnZOpe3tzfMzMzQsWNHdYVPRERERJQnVqwArKyEzxmXx+NiojuWXFkOnDkjWVxUcHG6pwLq5OGNwIvvxXT9GhEA0p7fmYiIKM9VqfJxaqX8pkqVrz6EqakpAGDatGno0qVLmmUqVKiQrWPq6uqifPnyuJnGOh43b96Erq6uuI7D9evXAQC1a9dWKWdkZITy5cvj1q1b2Tp3VhgbG0NTUxP9+vXD6NGj0yxTtqzqtJOhoaGpyoSGhqJEibRHfnp7e0NbWxv9VF7LylsZxXDjxg0IgpDqvsvlclStWhVnstEYCwgIQEBAACZPniwuhE5EREREVFAYGwPe3jK0apUic/8GzBzujNYTe6Oq313g0xSxRFnBTooCavvJl0CSQkz3/CZ78yATERHlKkPDLK/9UNBUqFAB9vb2uHHjBhYsWKC243bu3BnLly/Hs2fPUKZMGQDA27dvsXfvXnTo0EFc+8LS0hIAcOnSJdjY2Ij7R0RE4MGDB2jatKnaYkqmp6eHxo0bIyAgAM7OztDW1s50n23btmHSpEnilE/BwcG4cOEC+vfvn6psaGgojhw5gi5duqTbiZHbMosh5X13c3MT8+Pj43Ht2jWULl06y+fKD2tvEBERERF9jZYtgeHDgbVrP2VE2SLx5FL0qzcMVyeNh+KPDZLGRwULOykKqL8fqb6t2Kpd7iySSURERKmtXbsWrVu3RsuWLeHh4QErKyu8fv0ad+/exbVr17Br1y6x7JkzZ/Dq1SsAQFJSEoKDg7F7924AgJubG8zMzAB8nD7Kx8cHbdu2xZw5c6BQKLBo0SLExcWpTOHUpUsXzJo1CyNHjsTz589RvXp1hISEYOnSpXj//j3Gjx+vEutff/2FmJgYvH37FgBw584d8fxt2rSBnp5elq55xYoVcHFxgaurK0aOHAlbW1u8ffsWDx8+xKFDh3D69GmV8mFhYejcuTOGDh2KqKgoeHp6QkdHB9OmTUt17E2bNiExMTHDqZ6Cg4Nx9epVAMCjR48AQLwOW1tb1KpVK0vXkZ7MYnBxcUHt2rXh5eWF9+/fo1GjRoiKisLKlSsRFBQEHx8flVjt7OwwYMAAsUMiWVxcHLZu3YoGDRqgUqVKXxUzEREREZGUliwBjh8XEBT0aS26a0Nxs+I+eIZtxKLDXYF27aQNkAoMdlIUQG9CHiM0xXoUZuaBMDW1lzAiIiKioqVx48a4cuUK5s+fjwkTJiAyMhIlSpSAo6MjevTooVLW09NTZSogPz8/+Pn5AQB8fX3h7u4OADAzM8O5c+cwZcoUDBgwAImJiahfvz78/PxQsWJFcX8DAwNcunQJ8+fPx5o1a/D8+XOYmJigevXqWL16daoFn0eOHKmymPauXbvETpSgoCDY2tpm6ZodHR1x7do1zJ07FzNmzEBYWBiMjIxgb28vrkuR0oIFC3D16lUMHDgQ0dHRqFOnDrZv3w47O7tUZdevXw9bW1s0a9Ys3fP7+vpi4MCBKnndu3cHAAwYMAAbN27M0nWkJ7MYNDQ0cOLECSxZsgS7du3CTz/9BAMDAzg6OuLIkSNo3bq1WFYQBCQlJSEpKSnVcfbu3YvIyEgumE1EREREBV6xYsCGDTI0bixAED51VBz8Az+Oqox2M/rDpc49wNxc2iCpQGAnRQF0/OAmIGSGmG5YK1LCaIiIiAovd3d3CIKQ5jZnZ2fs2LEj02Mkd0hkhZ2dHfbt25dpOQsLC6xcuRLAxy/ElUolNDQ0xKmVUnry5EmWz58ZW1vbVCMD0qOjo4MVK1ZgxYoVmZa9f/9+pmU8PDzg4eGRpXOnJykpCYmJieLUWdmNwdDQEPPmzcO8efMyLGdra5tuvenduzd69+6d4f7JnRxKpTLTmIiIiIiIpOTmBkyYIMOyZZ8y3lkCf61E/yZ9ETCkLwwPHAPSaKcQpaQhdQCUfdtPhwPKz4ss9urjIGE0RERERAVDs2bNIJfL4e/vL3UoGTpw4ADkcjnXrCAiIiKiAmH+fKBChRQZN/sgKKQrhitOQPj0chVRRjiSogC68Ljc54QsCS3bGEkWCxERERVsSqUy0zf20xp5UJDUrFlTXM8C+Dh1VX7m7u5eoOIlIiIioqJNVxf43/+ABg0EJCV9GjVxeC12jLiMplsmY6i7O+DsLGmMlL8V7BZnERT57AH+e+kqpi0sAmFkVDGDPYiIiIjSN2fOHMyePTvDMtlZuyKjKbKkUqxYsa9eWDsvGRkZFah4iYiIiIjq1AGmTZNBnBk1tgSwayfG9nVDg+Gd4XT61sfeDKI0sJOigDm6fzPw0lNMN6wdLWE0REREVNANGzYM7dq1y7CMpaVlHkVDREREREQF1cyZwPHjwJUrnzKe10e83yL0qDEZV6eMh95vv0saH+Vf7KQoYHb4RQKCppj+pl+FDEoTERERZczS0pKdEERERERE9NW0tYGdO4Hq1QVERn6a9unSJNyx/hvjY9dh3cF2QIcO0gZJ+RIXzi5gLgTZfU7IEtGipaF0wRARERERERERERF9YmMD+PjIVDMPbMAfZcth+8I+QEiINIFRvsZOigLk9ePbePWykZi2tLyPYsUkDIiIiIiIiIiIiIgohbZtge+/T5ERbwjs3IWhrgl4NKwboFRKFhvlT+ykKECOHNgGhFYT0671YqQLhoiIiIiIiIiIiCgNc+cCjRqlyAitgXenl6OX5UV8WLxQsrgof2InRQGyw+8tUn5kvftVki4YIiIiIiIiIiIiojRoaQHbtwPm5sLnzH9GwD+iN6aenQmcOSNdcJTvsJOiALkUbC/+W6bxAU2bca4nIiIiIiIiIiIiyn9KlQK2bZNBJkvRUXHod6woWxHbf+jI9SlIxE6KAuLZ1ZMIT7EehVXpe9DXlzAgIiIiIiIiIiIiogw0aQLMnp1iIe0EfWDnbgyqn4hbA9sAHz5IFxzlG+ykKCBWb/4fEOYspps3lmVQmoiIiNRlx44dcHJygq6uLmQyGa5fv56t/WfMmIF27drBysoKMpkMHh4e6ZZ9/PgxunTpAiMjIxgYGKB58+a4du1aqnLR0dH44Ycf4ODgAH19fVhbW6NHjx64ffu2SrnTp09j0KBBqFixIvT19WFlZYWOHTvin3/+ydY15CZfX180b94c5ubmMDAwgLOzM3755RckJSWlKnvy5EnUr18fenp6MDU1hYeHB8LCwjI9h5+fH2Qymfjj7+8vbnN3d1fZ9uVPaGioWDY+Ph5LlixB5cqVoa+vj5IlS6J169a4cOFClq83PDwc48ePh62tLRQKhXiM169fi2X279+fbrxERERERAXNDz8ALVumyAh3ROzR39HZ4Qaipo6TLC7KP9hJUUD876qNSnrk6MoSRUJERFR0vHr1Cv369YOdnR2OHj2KixcvwsHBIVvHWLZsGSIiItChQwdoa2tneC5XV1c8ePAA69evx86dOxEXFwd3d3fcv39fpWz79u2xfPlyDB06FIcPH8aCBQtw/fp11K9fH8HBwWK51atX48mTJxg/fjyOHDmCFStWICwsDPXq1cPp06ezdzNywcmTJ9GsWTMkJiZi3bp12L9/P9zd3TF+/HhMmjRJpeyZM2fQunVrlCxZEgcOHMCKFStw8uRJNG3aFPHx8Vk632+//YaLFy+iUqXP63qtWrUKFy9eVPk5deoU5HI56tWrBwsLC7Hs0KFD8f3336NTp044dOgQfvvtN7x69Qpubm64cuVKpucPCQlB3bp1cfToUcycORMnTpzA6tWrUb58eXxI8QaZm5sbLl68iBkzZmTpuoiIiIiI8jMNDWDzZqB06RTTPt3qjYePR2HAf2uh3LpFuuAoX9CSOgDK3OPLR/HicTcxbWT8HLVqlZYwIiIioqLhwYMHSEhIQN++feHm5pajY7x9+xYaGh/fC/Hx8Um33JIlS/Dq1StcuHABNjYfX05wcXGBnZ0dZs2ahR07dgAAHj58iLNnz2LGjBn49ttvIQgClEol7O3t0bBhQ+zduxcTJ04E8PFLeXNzc5XztGrVCuXLl8eCBQvQpEmTHF2TumzcuBFyuRyHDx+G/qd5LJs1a4b79+9j48aNWLFihVj222+/hYODA3bv3g0trY+PsGXLlkXDhg2xfv16jBw5MtPzOTo6ol69eqnyvrRp0yYkJCRgyJAhYl58fDy2bt2K3r17Y968eWJ+w4YNYWlpiS1btqBOnToZnn/UqFGIj4+Hv78/jI2NxfwuXbqolDM2Nka9evVw7969TK+JiIiIiKggMDUFdu2SoVEjAQkJn2aIOboMBwb5Y5H3QEx3rgpU5kvZRRVHUhQAv/xvJ/BfVTHdtUM0ZJztiYiIKFd5eHjAxcUFANCzZ0/IZDK4u7uL2/39/dGhQweYmJhAR0cH1atXx86dO1MdJ7mDIjP79u1DkyZNxA4KAChevDi6dOmCQ4cOITExEQAgl8sBAIaGhir7GxkZAQB0dHTEvC87KADAwMAAjo6OePbsWZbiSikwMBC9e/eGubk5FAoFKlWqhN9++02lTPLUSps3b8akSZNgYWEBXV1duLm5ISAgQKWsXC6HtrY2dHV1U11Lyut48eIFrl69in79+okdFADQoEEDODg4YN++fdm+lox4e3vDwMAAPXv2FPM0NDSgoaGR6r4XL14cGhoaKvGm5cmTJzh48CCGDh2q0kFBRERERFRU1KsHLF2a4ktNpTawcxd+qFkcx0e3AqKipAuOJMWRFAXAdv/yKukJUyqlU5KIiCh/iIqLws2wm1KHkUoV8yow1DHMvCCAmTNnok6dOhg9ejQWLFiAxo0bo3jx4gA+rqPQqlUr1K1bF2vWrIGhoSG2b9+Onj174v379xmuO5GW2NhYPHr0CJ07d061zdnZGbGxsXj8+DEcHBxgY2ODjh07YtmyZahZsyZq1aqFp0+fYsKECbC2tkavXr0yPFdUVBSuXbuW7VEUd+7cQYMGDWBtbY2lS5fCwsICx44dw7hx4xAeHg5PT0+V8tOnT0eNGjXwxx9/ICoqCl5eXnB3d0dAQADKlSsHABgxYgS2bduGcePGYfr06dDT08OhQ4ewb98+LFy4UDzWrVu3xHuR1v05f/58tq4lI4GBgTh37hyGDBkCAwMDMV8ul2PUqFHw9vZGs2bN0KRJE7x+/RrTp0+HoaEhhg4dmuFxz507B0EQYGlpiW+++UbseKpXrx4WLlyI+vXrq+0aiIiIiIjyqzFjgPPngU8DxYFoa2DvZvTq3BbXhnWH7fZj4NvZRQ87KfK5uxcO4b+Hn6d6Mjd/hMqV7SSMiIiIKHM3w27CdYOr1GGkcm7gObhYu2SprJ2dnTgVkL29vco0QaNGjYKTkxNOnz4tvtnfsmVLhIeHY/r06ejfv3+WR1AAQGRkJARBgImJSaptyXkRERFi3q5duzB69GiVjgZnZ2ecOXMm07f0R48ejZiYGPzwww9Zjg8AJk2ahGLFiuHvv/8WO2uaN2+O+Ph4LFq0COPGjVM5t5mZGfbt2wfZpwaGi4sL7O3tsXDhQqxbtw4AULduXZw+fRrdu3cXR2Roampi4cKFmDx5snis5GtP7/6kvDdfy9vbGwAwePDgVNuWLVsGQ0NDdO3aFUqlEgBgbW2N06dPo3z58qnKp/TixQsAwJQpU9C4cWPs2bMHMTExmD17Npo0aYLLly+n2QlDRERERFSYyGTAunXAjRuAOLvpo1aI9P8BXR3m4u8Fc6D7g2eGx6DCh9M95XPLNx4CXn9eoLNX5w8ZlCYiIqLc9vDhQ9y7dw99+vQBACQmJoo/bdq0wcuXL1MtdJ1VsgzeGEq5beTIkdizZw+WLVsGPz8/bNu2Ddra2mjSpInKwtlfmjlzJrZs2SKOwsiquLg4nDp1Cp07d4aenl6qa46Li8OlS5dU9undu7dKzDY2NmjQoAF8fX3FvH/++QedO3dGzZo1cejQIZw+fRrTpk3DjBkzMHfu3AzvQVbysysxMRGbNm2Ck5NTqrUrAGD+/Pn46aef4OXlBV9fXxw4cAAVKlRA8+bNU01l9aXkTo3SpUtjz549aNmyJbp06YKjR49CQ0MDixcvVss1EBERERHld8WKAbt3A3p6KRbS9vPCtffNMfwfLwj790sWG0mDIynyMUEQsPtaRZW88VM51RMREZGU/vvvPwAf34ifMmVKmmXCw8OzdUxjY2PIZLI0RwS8fv0awOdRBEePHoW3tzd27dqFbt26iQtnt2zZEmXLloWXlxc2bNiQ6jizZ8/GvHnzMH/+fIwZMyZb8UVERCAxMRErV67EypUr0yzz5TVbWFikKmNhYYEbN26I6dGjR6NkyZLYt28fNDU1AQCNGzeGhoYGvLy80KdPH5QrVw4lSpQQ4/jS69ev0xxhkRNHjhxBaGgovvvuu1Tb7t69i1mzZmHx4sUqn3vr1q3h6OiISZMmqXTAfCn5Gpo1ayZeKwCUKlUKVatWxbVr19RyDUREREREBYGTE7BunQyf3v0CoAHs2QqfEdVRaWUPTCvnD3CkcZHBTop87Mbf+/A6sLuYtrK6i3Ll2ElBRET5XxXzKjg38JzUYaRSxbzKVx/D1NQUADBt2jR06dIlzTIVKlTI1jF1dXVRvnx53LyZeh2PmzdvQldXV1zH4fr16wCA2rVrq5QzMjJC+fLlxfUbUpo9eza8vLzg5eWF6dOnZys24GMniqamJvr164fRo0enWaZs2bIq6dDQ0FRlQkNDxS/rgY/X8s0336h8aQ98vDalUom7d++iXLlyqFy5MoCP96JNmzYqZW/evClu/1re3t7Q1tZGv379Um27ceMGBEFIdd/lcjmqVq2KM2fOZHjsjKZyEgQhW9ODEREREREVBr17f1yfYtWqTxmxpsDWQ5g+yBUVxjZDl923ATMzSWOkvMFOinxs6YaTQPTnLz/6d8+gMBERUT5iqGOY5bUfCpoKFSrA3t4eN27cwIIFC9R23M6dO2P58uV49uwZypQpAwB4+/Yt9u7diw4dOohrX1haWgIALl26BBsbG3H/iIgIPHjwAE2bNlU57ty5c+Hl5YUZM2akWtw6q/T09NC4cWMEBATA2dkZ2trame6zbds2TJo0SZyKKTg4GBcuXED//v3FMpaWlvD390dSUpJKR8XFixcBfJwaCQCsrKxQp04dbN68GVOmTBHLXrp0Cffv38eECRNydF0phYaG4siRI+jSpYtKR0rKWJPP6ebmJubHx8fj2rVrYqzpqVu3LkqXLo3jx4+rXG9ISAhu3LiB3r17f/U1EBEREREVND//DPj7A1eufMr4rxqwayf6du2Avwe0QI39l4EstD+oYGMnRT4lKJU4GJDybU8lRk/hKAoiIqL8YO3atWjdujVatmwJDw8PWFlZ4fXr17h79y6uXbuGXbt2iWXPnDmDV69eAQCSkpIQHByM3bt3AwDc3Nxg9unNoClTpsDHxwdt27bFnDlzoFAosGjRIsTFxcHLy0s8XpcuXTBr1iyMHDkSz58/R/Xq1RESEoKlS5fi/fv3GD9+vFh26dKlmDVrFlq1aoW2bdumWjcirXUX0rNixQq4uLjA1dUVI0eOhK2tLd6+fYuHDx+K60mkFBYWhs6dO2Po0KGIioqCp6cndHR0MG3aNLHMxIkTMW7cOLRv3x7Dhw+Hnp4eTp06haVLl6JZs2aoWrWqWPbHH39E8+bN0b17d4waNQphYWH4/vvvUblyZQwcODDL15GeTZs2ITExEUOGDElzu4uLC2rXrg0vLy+8f/8ejRo1QlRUFFauXImgoCD4+PiIZYODg2FnZ4cBAwaIC3FraGhg2bJl6NGjBzp27IiRI0ciJiYGc+fOhba2tsp9ISIiIiIqKhSKj+tT1KsHhIR8ynzYGrEnVqGd2zD4jxkAy7VbP664TYUWOynyqUt+uxD9oKuYLmtzE1ZWVTPYg4iIiPJK48aNceXKFcyfPx8TJkxAZGQkSpQoAUdHR/To0UOlrKenp8pUQH5+fvDz8wMA+Pr6wt3dHQBgZmaGc+fOYcqUKRgwYAASExNRv359+Pn5oWLFz2tUGRgY4NKlS5g/fz7WrFmD58+fw8TEBNWrV8fq1atVOh4OHToE4OM6FkePHk11HYIgpMpLj6OjI65du4a5c+dixowZCAsLg5GREezt7VNNwQQACxYswNWrVzFw4EBER0ejTp062L59O+zs7MQyY8eOhZWVFZYtW4YhQ4YgNjYWtra28PT0xMSJE1WO5+7ujiNHjmDWrFlo37499PT00K5dOyxZsgQKhSLL15Ge9evXw9bWFs2aNUtzu4aGBk6cOIElS5Zg165d+Omnn2BgYABHR0ccOXIErVu3FssKgoCkpCQkJSWpHKNbt27Yt28f5s+fj27dukGhUMDNzQ07duxQuS9EREREREVJmTLAn38Crq4C3r371BlxbSheGgWhQ/mFOLu8OvQmTpU2SMpV7KTIp5ZuOA+87ymmh/TmsCYiIqK85u7unu4X+c7OztixY0emx0jukMgKOzs77Nu3L9NyFhYW4gLWyQtna2hoiFMr5eTcWWFrayuODMiMjo4OVqxYgRUrVmRYrkuXLumu7fGl5s2bo3nz5lkqm5akpCQkJiaKU2eldP/+/Uz3NzQ0xLx58zBv3rwMy9na2qZbbzp27IiOHTtmuH9yJ4dSqcw0JiIiIiKiwqBaNWD3bhnathWQlPSpXXN6Af4xCsaAS99hx1+VodE69ctRVDhwhb58SKlMwtHrNT5nyBIxbBKneiIiIiL6Gs2aNYNcLoe/v7/UoWTowIEDkMvlGDx4sNShEBERERHlmZYtgTVrvpjWaf8G7NZ1g9cvnYHbt6UJjHIdR1LkQ2dObkdMYCcxXdEuAKamtaULiIiIiAotpVKZ6Rv7aY08KEhq1qyJq1evimlHR0cJo8mcu7t7gYqXiIiIiEhdhgwBnjwB5s//lKHUBnbsw9zBDVB+VEP0X/8PwKlSC52C3eIspJauvw7E9xHTI/oVky4YIiIiKtTmzJmD2bNnZ1gmKCgItra2WTpeRlNkSaVYsWKoVauW1GFkmZGRUYGKl4iIiIhInebO/dhRsWXLp4w4Y2DzXxg4qD7M+jdA6x3/AKVLSxkiqRk7KfKZpKREnPq3ppiWacZh4ISKGexBRERElHPDhg1Du3btMixjaWmZR9EQEREREVFRJ5MB3t7AixeAuMxelC2UW46jax83+PZsiLr7rgLm5lKGSWrETop85tjRrYh72FVMV3W4huLFG0gYERERERVmlpaW7IQgIiIiIqJ8RaEA9u4FGjYUcPfup3UqwqogdudfaNOtGS50aYQKhy4CxsbSBkpqwYWz85ml6+8CCfpietRA/qIRERERERERERFR0WJsDBw9KkOZMimmk31RF68PHETz6sEI6dQUePtWugBJbdhJkY8kJH7AuZv1xbSm/C36jK4kYURERERERERERERE0rC2Bk6elMHMPEVHxZPGeHZ8J1pWvIk3XdoAsbHSBUhqwU6KfGT/QR8kPGolpmtV+gd6ehIGRERERERERERERCQhBwfg5AkZDI2UnzMftMetC5vQwfIC4np0AT58kC5A+mrspMhHVmx8Cii1xfT4IRYSRkNEREREREREREQkPWdn4OhfGtDTT9FRcas3zt1ehW/0jiKxf18gKUm6AOmrsJMin4hPiMPlWy5iWq6IRNfhFSWMiIiIiIiIiIiIiCh/qFcPOLBfA3LtFB0V/wzH/uc/YlDcLihHDAcEIf0DUL7FTop8YsdeHyQ+aSKmGzj9A23tDHYgIiIiIiIiIiIiKkKaNQN27dSApmaKzogLU+ETtgQjX3pD+HYKOyoKIHZS5BMrN/0HCJpievIoawmjISIiIiIiIiIiIsp/OnYENm6UqWZenILfX67F+FvLIcybJ01glGPspMgH3n+IwbVbbmJaofsf2ng4SBgRERERJduxYwecnJygq6sLmUyG69evZ2v/GTNmoF27drCysoJMJoOHh0e6ZR8/fowuXbrAyMgIBgYGaN68Oa5du5aqXHR0NH744Qc4ODhAX18f1tbW6NGjB27fvp3Nq8sdMpkszZ9FixalKnvs2DE0bNgQurq6MDQ0RPv27bN8Hba2tuKxx4wZI+Zv3Lgx3Ri+jMPd3T3DsqGhoZnGsWXLFlSvXh06OjowNTVF79698ezZs1TljIyM0oyXiIiIiIiyp29f4PffAZksxaiJa8Ow8sVmfO87B8Ivv0gXHGWbltQBELBp2xYonw0T042dr0FTs7WEEREREREAvHr1Cv369UOrVq2watUqKBQKODhk70WCZcuWwdnZGR06dMD69eszPJerqyuMjY2xfv166OjoYOHChXB3d8fVq1dRoUIFsWz79u3h7+8PLy8v1KxZE8+ePcO8efNQv3593Lx5EzY2Njm+ZnXp1q0bJk+erJJnba06UvTAgQPo3LkzOnbsiD179iAqKgqzZ8+Gq6srrl69Cjs7u0zP06ZNG8ycORMWFhZiXtu2bXHx4sVUZWfNmoUTJ06gc+fOYt6qVasQHR2tUu79+/do1aoVatasqXLctKxcuRLjxo3DkCFDsGjRIjx//hwzZ86Eq6srAgICYGxsLJY9efIkEhMTUb9+/Uyvi4iIiIiIMjZ0KKCvL0O//kookz69i3/rGyx2MIBiZw/MMTQEBgyQNkjKEnZS5AOrN79RSX87tpw0gRAREZGKBw8eICEhAX379oWbm1vmO6Th7du30ND4+MDs4+OTbrklS5bg1atXuHDhgtjJ4OLiAjs7O8yaNQs7duwAADx8+BBnz57FjBkz8O2330IQBCiVStjb26Nhw4bYu3cvJk6cmKNY1alkyZKoV69ehmW+++47VKlSBXv37oVM9nG4doMGDeDg4IBZs2Zhy5YtmZ7HzMws1XnMzMxgZmamkhcTE4OLFy/CxcVFpcPH0dEx1TE3bdqEhIQEDBkyJMNzx8fHY+bMmWjfvj3WrVuncswGDRrgp59+wvz588X8WrVqZXo9RERERESUdb17AwYGGujaPRGJHz591f2gPeZ++BPaqzthRrFiQJcu0gZJmeJ0TxJ7GxeNW7eaimk9g6do3LtCBnsQERFRXvDw8ICLiwsAoGfPnpDJZHB3dxe3+/v7o0OHDjAxMYGOjg6qV6+OnTt3pjpOcgdFZvbt24cmTZqojIIoXrw4unTpgkOHDiExMREAIJfLAQCGhoYq+xsZGQEAdHR0snyNAPDhwwfMmzcPFStWhEKhgJmZGQYOHIhXr16plLO1tUW7du2wb98+ODs7Q0dHB+XKlcMvORxGHRERgfv376N169ZiBwUA2NjYoHLlyti/fz+SkpJydOy07NixA+/evcu04wEAvL29YWBggJ49e2ZY7tatW4iKikKbNm1U8uvXrw8TExPs2bPnq2ImIiIiIqLMdegAHD2iBW3dhM+ZT5pg5vPj+HHhMODTC1+Uf3EkhcTW/m8bhNDhYrplteuQybhoNhERFWxRUcDNm1JHkVqVKsAX3+2na+bMmahTpw5Gjx6NBQsWoHHjxihevDgAwNfXF61atULdunWxZs0aGBoaYvv27ejZsyfev3+f4boTaYmNjcWjR49UpiFK5uzsjNjYWDx+/BgODg6wsbFBx44dsWzZMtSsWRO1atXC06dPMWHCBFhbW6NXr15ZPq9SqUTHjh1x7tw5TJ06FQ0aNEBwcDA8PT3h7u4Of39/6OrqiuWvX7+OCRMmwMvLCxYWFtiyZQvGjx+PDx8+YMqUKSrH3rp1K7y9vaFUKlG5cmWMGTMGAwcOFLd/+PABAKBQKFLFpVAo8P79ezx69Cjb02ulx9vbG8WLF0f37t0zLBcYGIhz585hyJAhMDAwyLBsZtcQGBiIuLi4bHccERERERFR9jRtCvidkqNpyzjEvv30/P2iHr5P8EX40nZYHBEB2ahR0gZJ6WInhcT+2Bqnkv5uYiWJIiEiIlKfmzcBV1epo0jt3Dng0+CITNnZ2YlTAdnb26tMKTRq1Cg4OTnh9OnT0NL6+DjVsmVLhIeHY/r06ejfv3+WR1AAQGRkJARBgImJSaptyXkRERFi3q5duzB69Gg0adJEzHN2dsaZM2dU1kDIzM6dO3H06FHs2bMHXVIMga5atSpq166NjRs3YuTIkWJ+SEgIAgICULVqVQBA69atERYWhrlz52LUqFHQ09MDAPTu3Rtt27ZFmTJlEBYWBm9vbwwaNAiPHz/G3LlzAXycDsrExATnz59XienNmze4detWqmv+Gvfu3cOFCxcwfPhwMcb0eHt7AwAGDx6c6XErVKgADQ0NnD9/XqUD5tGjR3j58iWAj59tqVKlviJ6IiIiIiLKivr1gYvndNCw8TvERH564SisCn6KuYzgzR2xPSIcGjNmAilGclP+wOmeJPQ65jXu32ohposb3kedzvYSRkRERESZefjwIe7du4c+ffoAABITE8WfNm3a4OXLl7h//36Oji3L4GE55baRI0diz549WLZsGfz8/LBt2zZoa2ujSZMmCA4OzvL5Dh8+DCMjI7Rv317lOqpVqwYLCwv4+fmplHdychI7KJL17t0b0dHRuHbtmpi3ZcsW9O7dG66urujatSuOHDmCdu3aYdGiReI0UhoaGhg9ejROnTqFuXPnIiwsDA8fPkTfvn3x/v17sYw6JHc8ZDbVU2JiIjZt2gQnJ6dM19MAPnYg9enTB//73/+wdu1avH79Gv/++y/69OkDTU1NAOq7BiIiIiIiylzVqsC1SwYwNI/6nBljgV1XzqDhn3cQP2kcoFRKFyClia0mCf3qvROI+Dxyon2N2+zIIyIiyuf+++8/AMCUKVMgl8tVfkZ9Gj4cHh6erWMaGxtDJpOlOXLg9evXAD6PqDh69Ci8vb2xdu1aTJgwAY0aNUL37t1x/PhxvH79Gl5eXtm6ljdv3kBbWzvVtYSGhqa6DgsLi1THSM7LbNRD3759kZiYCH9/fzFv1qxZmDhxIubNm4eSJUvC3v7jyxrJoxKsrKyyfC3pSUhIwP/+9z9UrVo104Wrjxw5gtDQ0CytW5Fs9erV6NmzJ0aNGoUSJUqgevXqqFixItq2bQuFQoESJUp87SUQEREREVE2ODgAdwIMYVMp7HNmkg4uXd6OSr4miBrYB0hISP8AlOc43ZOENu4UVNLfTaksUSRERETqVaXKx6mV8psqVb7+GKampgCAadOmqUyRlFKFChWydUxdXV2UL18eN9NYyOPmzZvQ1dVFuXLlAHxcFwIAateurVLOyMgI5cuXF6dKygpTU1OUKFECR48eTXN7sWLFVNKhoaGpyiTnZfZlvCB8fO5JObJAS0sLP//8M+bMmYOgoCCYmpqiVKlSaNmyJcqWLYvSpUtn+VrSc/jwYYSFhWHmzJmZlvX29oa2tjb69euX5ePr6+vDx8cHv/zyC549ewZLS0uYmpqiYsWKaNCggTgdGBERERER5R1LS+COvzmadn6BS8c/v/wUdGM2bGO2IqBbJ9hu3w2kWIOPpMNWk0TC3oYh6HYrMV3C5AaqtKmawR5EREQFh6Fh1td+KGgqVKgAe3t73LhxAwsWLFDbcTt37ozly5fj2bNnKFOmDADg7du32Lt3Lzp06CB+2W1paQkAuHTpEmxsbMT9IyIi8ODBAzRt2jTL52zXrh22b9+OpKQk1K1bN9Pyt2/fxo0bN1SmfNq6dSuKFSuGGjVqZLivj48P5HI5atasmWqbgYEBqnzqQbp27RpOnTqFpUuXZvk6MuLt7Q0dHR1xeq70hIaG4siRI+jSpUuORj8YGxuL64EcPHgQ9+/fx48//pijmImIiIiI6Ovp6QHn/7LCgHFPsfk3azH/zcPeqPiuLHxbtEP9HT4fezRIUuykkMjSVfuAN8PFdOea9wGwk4KIiKggWLt2LVq3bo2WLVvCw8MDVlZWeP36Ne7evYtr165h165dYtkzZ86I6zAkJSUhODgYu3fvBgC4ubnBzMwMwMfpo3x8fNC2bVvMmTMHCoUCixYtQlxcnMoUTl26dMGsWbMwcuRIPH/+HNWrV0dISAiWLl2K9+/fY/z48Vm+jl69emHLli1o06YNxo8fjzp16kAul+P58+fw9fVFx44d0blzZ7G8paUlOnToAC8vL5QqVQqbN2/GiRMn8OOPP4oLUi9ZsgR37txB06ZNUbp0aXHh7OPHj8PLy0sciQIAfn5+uHr1KpydnSEIAq5cuYIff/wRrVq1wpgxY7L/wXwhJCQER48eRc+ePTNdUHzTpk1ITEzMcKqnpk2b4syZM0hMTBTz9uzZg5CQEFSqVAlxcXHw8/PDihUrMGLECHTs2PGrr4GIiIiIiHJOQwPw+dUaFSs8w4yJJYEkbQBAfGh9NHzng5lNx2D2ukmF9y27AoKdFBLZsk+ukv7+u+oSRUJERETZ1bhxY1y5cgXz58/HhAkTEBkZiRIlSsDR0RE9evRQKevp6YkzZ86IaT8/P3FBal9fX7i7uwMAzMzMcO7cOUyZMgUDBgxAYmIi6tevDz8/P1SsWFHc38DAAJcuXcL8+fOxZs0aPH/+HCYmJqhevTpWr16dpQWfk2lqauLgwYNYsWIFfHx8sHDhQmhpaaF06dJwc3MTRzckq1atGgYOHAhPT08EBgbC0tISP//8MyZOnCiWqVixIg4ePIg///wTkZGR0NXVRbVq1bBt2zb06tVL5Xja2trYs2cP5s2bh/j4eNjb22POnDkYN26cuPD019i4cSOSkpKytMbE+vXrYWtri2bNmqVbJikpCUlJSSp5mpqaWL9+PQIDA6FUKuHk5IS1a9eK62oQEREREZH0fhhbBg7lX+Kb7tpIivk4clp4Z4k59/biQL9fcGbsNRhOHAsuGCwNmZA8QTDliJOTExITE3H37l2VOZYz8jwyBGWsZcC7UgAAC7PLeBmW+RQLVDAplUqEhYXB3Nw8y3WEihbWEcpIQagfsbGxCAwMhL29PXQ5n2eeEwQBSqUSGhoakOXiA7WtrS0qV66Mw4cP59o5ssvW1hZubm7w9vaGhoZGvv0dSZaUlARBECCXyzF69Gj8+uuvX33MrPz+OTk5Afg4XRflPzlpT1DRUhCeBUharCOUEdYPykxRqyP+t1/DrXkk3r+0U8nXMb6F3Y3+QNutCz7OE0UA8q4tUfhrXj40Z/lesYMCAHrWeSxhNEREREQF1//+9z/I5XKMGzdO6lAyVaJECcjl8swLEhERERFRrqjlZIKXD6xRs8M5lfy4yMpod/hHDKj5M5SPgiSKrujidE957Pjds/hjfYoFI2VJmDqttnQBERERUaGUct2EtBSEkQeZOXToEOLj4wEA5ubmEkeTOT8/P/FzKQjxEhEREREVRsUN5PA/4Io5667Ca5I1hHclP25IUuB/92bgVP3TOD3zGhzGdOH0T3mEnRR56G7YA7TrEgnh+edFFCuan4FlwyYSRkVERESFzZMnT1C2bNkMy3h6eqosyJ2VY+Y3X66Zkd9Vq1ZN6hCIiIiIiOiTWUNro1PTl2jc0RevbzUW81+8aoKKk2LQd+0yrNvQFIraVSWMsmhgJ0UeCY+JQP2OZ5Fw7/PCjQrtcBxcyCH/REREpF6Wlpa4evVqpmWIiIiIiIiKMudypRB63Qydxm/Hkd/bAwn6AAAhUR8+tydhb/M7+LXxbHisGw2YmkocbeHFToo8EJ8Yjzq91iPq0rdinoZmLI5PPgf7gZ0ljIyIiIgKI21tbdSqVUvqMIiIiIiIiPI9uaYW/vy1Fza0O4PhQxVIeF5P3BYT5YiB+z2xyH8bDgyLQIVpIwAtfqWubgV7IuICQBAENB/1M4IOf5siV4kN3Xeh0QJ2UBARERERERERERFJbWArN4Q9cETLEZsA3QiVbfeff4NK8/qhV7XZiD1wGBAEiaIsnNhJkcuGzluJc+snqeTNbLQR/bf2kygiIiKi3KFUKqUOgajI4e8dEREREZH6GOkWx9HVA3DxeiTK1N+vsk34YIgdt+fCsH8lDK87DfF+Z6UJshBiJ0UuWuKzDd4L+gFJCjGvp+MWzDk9gCvDExFRoaGtrQ0AiImJkTgSoqIn+fcu+feQiIiIiIi+Xj2H8nh6oROWbjsHhcVNlW0J0Xb4/eoiFO9cEmNdf8CHgOvSBFmIcAKtXHLg7FlMHdsAiDMW8+qWOYytV7oAmpoSRkZERKRempqaMDExQWhoKABAX18fGhp8DyKvCIIApVIJDQ0NyPgSRJGhVCoRExOD0NBQmJiYQJPPl0REREREajeplytGdP6A3pOO4IB3AyDeSNz24U0F/Pr3fKxrehujas7C4lX9oWVfXrpgCzB2UuSCfx/dR9cexYEoGzHP2uw8zlxuAA19XQkjIyIiyh1WVlYAIHZUUN4RBAGCIEAmk7GToggyMTERf/+IiIiIiEj99BTa2P9bGzz+IRL9xx3B+UOuwIdi4vb4SCcsOzkHq+rdQnv7+VgytBxs+3YBFIoMjkopsZNCzV5FvUaD5s+R9F9TMa+44R0EnCwDRSkTCSMjIiLKPTKZDKVLl0apUqXw4cMHqcMpUpRKJV6/fg0TExOOYClitLW1OYKCiIiIiCiPlLM0xt+72+D+0wgMGHMEl4+6AQn64vb415Wx+3Jl7PaPQ7k5ezG57gOMmN4JGtWqShh1wVCkOymuXr0KT09PXLx4ER8+fICTkxMmTJiA3r175+h4HxITUNXtJGKCeoh5cr0QXN2WABNna3WFTURElG9pampCV5ejBvOSUqmEXC6Hrq4uOymI8pC62xJEREREVDBUsC6BSwfb4HbQKwwY4Yd/TjcBElO0g5N08PjpNxj9FJh4LBCtrBdhTgd9VO3XHKhQgWsVp6HItmT9/Pzg4uKCc+fOoVu3bhg5ciTCw8PRp08fLFiwINvHEwQB9VpuxMsbnzsoZPK3+Ounu3Bozd4yIiIiIqLCQt1tCSIiIiIqeJzKmsH/WFtcvxONBm0PQsMg9fTHH6LtcfDW96i2YCx06mqiermNmNFmMYI3HQJiYiSIOn+SCYIgSB1EXktMTETFihXx/PlzXLx4EdWrVwcAvH37FvXr18f9+/dx584d2NvbZ3osJycnJCYmokqtSdizdfjnDRoJWDv5IIYt7ppbl0EFhFKpRFhYGMzNzfmGK6WJdYQywvpBmWEdocw4OTkBAG7fvi1xJIWDOtsSwOf2xN27d/k7TGni33nKDOsIZYT1gzLDOqI+sfEJmLPqPLzXCXh1rxEgZDw1q77xv6ha6hoalI+Fay0zuLWpAsNq5YF8NKVrXrUlimTNO336NB49eoTevXuLjQoAKFasGGbOnInExERs2LAhy8eLCH+LPdsGq+RN6bGNHRRERERERIWMutsSRERERFQ46CrkWDjRHWF3GuPCzRdw67UfmsaP0y0fE+mMC3c88NPBkeg4qxuMalWAtmEILEr7om71rRjUaT0WjN+G3b8ex71z95EQU3jXfyySa1L4+fkBAFq0aJFqW3LemTNnsny8iNeGSHkrOzT6H5Zs6/9VMRIRERERUf6j7rYEERERERU+9Z2s4bfNGvEJCVh78BL27H6OgCvmePu0tur6FV9IiCmD/2LK4L8XwJXrX25VQlM3DAq9MOjrvYZCHgdd7TjoKT5AVycBBjpJMNBTQlchQC4H5HJAS+vjv7W1ZdDSlkFLE9DUkEFDJoOmpgyaGhrQ1PyY1tBIvVZGxKtolDArrtZ7k5Yi2UkRGBgIAGkOwTY2NoapqalYJms+D0ipUnk39vv2+9oQiYiIiIgoH1J/W4KIiIiICiuFXI5xXeth3KcJd0IiI7Bu51kc3BeOO7ftEBdSA1BqZ/FoGkiKNcf7WHO8j8i1kL8wGyXMcv8sRbKTIioqCgBgaGiY5vbixYvj+fPnaW6Lj49HfHy8mH769CmABABO0JTHIDFRD5WreKo7ZCrgEhMToaVVJH/dKItYRygjrB+UGdYRysijR48gl8ulDqPQ+Jq2BJB2e+LDhw+oUqWKegOlQoV/5ykzrCOUEdYPygzriDTKFQcS9RMRE/sB72MSEB8v4EOCBpKStCEkaiN/rNTwCE+f5n5bgrUvmxYuXIjZs2er5GlqasLBQYBMpi9RVJSfCYKAyMhImJqaQiZLPWyKiHWEMsL6QZlhHaHMyOVyCIKA+Ph4KBQKqcMp8tJrTwiCwN9hShP/zlNmWEcoI6wflBnWEWlpaWrB0EALhgZSR5K2p0/zpi0hEwRByLWj51Pdu3fH7t274e/vj5o1a6babmZmBplMhrCwsFTbvnzzKTo6GmXKlEFUVBSKF8/9+bmo4ImOjoahoSHrCKWLdYQywvpBmWEdocywjqjX17QlALYnKPv4O0yZYR2hjLB+UGZYRygjeVU/8sOYkTyXPH9sWnPFRkZGIjw8PM05ZgFAoVCgePHiKj9ERERERFQ0fE1bAmB7goiIiIjoS0Wyk8LNzQ0AcPz48VTbkvOSyxARERERESVjW4KIiIiISL2KZCdF06ZNUa5cOWzduhXXr18X89++fYu5c+dCS0sLHh4eksVHRERERET5E9sSRERERETqVSQXztbS0sIff/yBli1bwtXVFd988w2KFy+OvXv3IigoCPPmzYODg0OWjqVQKODp6clFCCldrCOUGdYRygjrB2WGdYQywzqiXupsSwD8fChzrCOUGdYRygjrB2WGdYQyklf1o0gunJ3sypUr8PT0xMWLF/Hhwwc4OTlhwoQJ6NOnj9ShERERERFRPsa2BBERERGRehTpTgoiIiIiIiIiIiIiIpJOkVyTgoiIiIiIiIiIiIiIpMdOCiIiIiIiIiIiIiIikgQ7KXLo6tWraNOmDYyNjaGvr486depg69atUodFeWzz5s0YPnw4atWqBYVCAZlMho0bN6ZbPjo6GpMmTYKNjQ0UCgVsbGwwadIkREdH513QlGdevHiB5cuXo0WLFrC2toa2tjYsLCzQtWtXXL58Oc19WEeKjjdv3mDcuHGoX78+LCwsoFAoYGVlhSZNmmDPnj1IazZG1g9avHgxZDIZZDIZLl26lGYZ1pOixdbWVqwTX/6MGDEiVXnWj7yhjraCUqnEr7/+CmdnZ+jq6sLMzAw9evRAYGBgLkVNeelr68jff/+NyZMno2bNmihRogR0dHRQsWJFfPfdd3jz5k3uBU55Qt3fNyQkJKBatWqQyWSoWLGiGiMlqairjrx9+xaenp6oXLky9PT0YGRkhBo1amD27Nm5EDXlJXXUkTdv3mDWrFlwdnZGsWLFYGpqitq1a+PXX39FXFxcLkVOuS2732WmR93PqlyTIgf8/PzQsmVLaGtro1evXjA0NMTevXsRFBSE+fPnY/r06VKHSHnE1tYWwcHBMDU1hb6+PoKDg7FhwwZ4eHikKhsTEwMXFxdcv34dzZs3R40aNXDjxg0cPXoU1apVw99//w19ff28vwjKNd9//z1+/PFH2NnZwc3NDebm5ggMDMT+/fshCAK2bduGHj16iOVZR4qWhw8folq1aqhXrx7Kly8PExMThIWF4dChQwgLC8PQoUPx+++/i+VZP+ju3buoXr06tLS0EBMTg4sXL6JevXoqZVhPih5bW1u8efMGEyZMSLWtVq1aaNeunZhm/cgb6morDBs2DOvWrYOjoyPatm2L//77Dzt27ICOjg4uXLgAR0fHXL4Syi3qqCMWFhYIDw+Hi4sLqlevDplMBj8/PwQEBMDOzg4XLlyAubl5HlwNqVtufN8wa9Ys/Pzzz4iJiUGFChVw7969XIic8oq66sjTp0/RpEkTPH78GM2aNUP16tURHx+Phw8f4unTp/j3339z+Uoot6ijjrx58wY1a9bE48eP4eLigrp16yI+Ph5//fUXHj16hCZNmuDEiRPQ0OD77wVNdr7LzIjan1UFypaEhATBzs5OUCgUwrVr18T86OhowcnJSdDS0hIePHggYYSUl06cOCE8efJEEARBWLhwoQBA2LBhQ5plZ82aJQAQpk6dmmb+rFmzcjtcymN79uwRzp49myr/7NmzglwuF0xMTIS4uDgxn3WkaElMTBQSEhJS5UdHRwuOjo4CAOHWrVtiPutH0ZaYmCjUrl1bqFOnjtC3b18BgHDx4sVU5VhPih4bGxvBxsYmS2VZP3KfutoKp0+fFgAIrq6uKs8KJ0+eFGQymdCoUaNciZ9yn7rqyKJFi4SQkBCVPKVSKYwcOVIAIIwaNUrtsVPuy43vG/755x9BS0tL+OWXXwQAQoUKFdQdNuUhddWR5GdLXV1d4fTp02mehwomddWRH3/8UQAgTJw4USU/Pj5eqF27tgBAOHPmjNrjp9yXne8y05Mbz6rspMimY8eOCQCEgQMHptq2fft2AYAwbdo0CSIjqWX0i61UKgVLS0vBwMBAePfuncq22NhYwdjYWLCyshKUSmUeRUtSa9GihQBAuHr1qiAIrCOkauLEiQIAYf/+/YIgsH6QIMyfP1/Q1tYWbt26JQwYMCDNTgrWk6Ipq50UrB95Q11thW+++Sbdxn+rVq0EAML9+/fVEjPlrdxuT4aEhAgABCcnp68JkySi7voRHx8vVKlSRXBxcRGUSiU7KQoBddWR5LIzZ87MjTBJQuqqI8OHDxcACCdOnEi1bfr06QIAYdeuXWqJmaST006K3HhW5ZicbPLz8wMAtGjRItW25LwzZ87kZUhUAAQGBiIkJAQNGzZMNY2Cjo4OGjVqhBcvXuDhw4cSRUh5TS6XAwC0tLQAsI7QZ3FxcTh9+jRkMpk4PJL1o2i7desWZs+ejRkzZsDJySndcqwnRVd8fDw2bdqEBQsWYPXq1bhx40aqMqwfeUNdbQU/Pz/o6+ujYcOGqba1bNkyy8eh/Ce325NfPmNSwaLu+uHl5YXAwEB4e3tDJpOpJUaSlrrqyI4dOwAA3bt3x7Nnz7BmzRosWrQIu3btwrt379QXMOU5ddWR5HbH0aNHVfITEhJw8uRJ6Orqon79+l8ZLRVUufGsyieXbEpe/MPe3j7VNmNjY5iamnIxO0olo3qTMj8wMDDdMlR4PH36FCdPnoSFhQWqVKkCgHWkKHvz5g2WL18OpVKJsLAwHDlyBM+ePYOnp6fK5w6wfhRFiYmJ8PDwQKVKlfD9999nWJb1pOgKDQ1NNYdsq1at4OPjA1NTUwCsH3lFHW2FmJgYvHz5EpUrV4ampmaq7V/+30AFS263J9evXw8g7S+nKP9TZ/24evUqFi9ejAULFsDBwUGtcZJ01FVH/P39AQB///03Jk6ciPj4eHGbmZkZdu7cCXd3d/UETXlKXXVkyJAh8PHxwdKlS+Hv74/atWsjPj4eR48eRWRkJLZu3QorKyu1x0/5X249q7KTIpuioqIAAIaGhmluL168OJ4/f56XIVEBkJV6k7IcFV4JCQno168f4uPjsXjxYvEPOutI0fXmzRvMnj1bTMvlcixZsgSTJ08W81g/iq4FCxbgxo0buHz5svh2bHpYT4qmQYMGwc3NDU5OTlAoFLhz5w5mz56Nv/76Cx06dMD58+chk8lYP/KIOtoK/KwKt9xsT16/fh2zZ8+Gubk5pk6dmuMYSTrqqh/x8fHw8PBA9erVVZ4pqeBTVx0JCwsDAIwdOxZTpkzBmDFjoKOjg23btmHKlCno1KkT7t69i1KlSqkveMoT6qojurq68PPzw/Dhw7F582bxrXgNDQ2MGTMGLi4u6guaCpTcelbldE9ERHlEqVRi0KBBOHv2LIYOHYp+/fpJHRLlA7a2thAEAYmJiQgKCsKcOXPwww8/oGvXrkhMTJQ6PJLQjRs3MG/ePEyZMgU1atSQOhzKp2bNmgU3NzeYmpqiWLFiqFu3Lg4fPgwXFxdcvHgRR44ckTpEIsoDQUFBaNeuHZKSkrB9+3ZxFBUVTTNnzkRgYCDWr1+f5luuREqlEgDQrl07LFq0CKVLl4apqSnGjh2LiRMnIioqCt7e3hJHSVIKDw9H8+bNcenSJfz555948+YNQkNDsWbNGmzYsAF169ZFZGSk1GFSIcJOimxK7iVKrzcoOjo63Z4kKrqyUm9SlqPCRxAEDB06FJs3b0bfvn2xZs0ale2sI6SpqQlbW1t8//33mDdvHvbt24d169YBYP0oqgYMGAA7Ozt4eXllqTzrCSXT0NDAwIEDAQDnz58HwPqRV9TRVuBnVbjlRnsyODgYjRs3xqtXr7B79240btz4q+Mkaaijfly7dg0///wzfvjhB3FqWSo81PU3JLlMhw4dUm1r3749gM9TQlHBoq46MmnSJFy4cAF79uxBmzZtYGhoiJIlS2Lo0KFYvHgxHj9+jOXLl6szdCogcutZlZ0U2ZTRvFqRkZEIDw/nPL6USmbzsWU2TzQVbEqlEoMHD8b69evxzTffYOPGjdDQUP3zyzpCKSXPI5286BnrR9F048YN3Lt3Dzo6OpDJZOLPpk2bAAD169eHTCbD/v37AbCekKrkt6jfv38PgPUjr6ijraCvr49SpUohKCgISUlJqbbzsyrY1N2efPLkCdzd3RESEoKdO3eiXbt2aouV8p466se///6LpKQkeHl5qTw/JC+cff/+fchkMhgZGak9fsp96vobUqFCBQBIsx4k58XGxuY8UJKMuurIn3/+CRMTEzg7O6fa1qRJEwDAP//885XRUkGUW8+q7KTIJjc3NwDA8ePHU21LzksuQ5TM3t4elpaWOH/+PGJiYlS2xcXF4ezZs7C0tET58uUlipByi1KpxJAhQ7Bhwwb07NkTPj4+6S4sxDpCyUJCQgAAWlofl45i/SiaBg8enOZP8sNehw4dMHjwYNja2gJgPSFVly9fBgDWjzymrraCm5sbYmJixJEwKR07dizLx6H8R53tyeQOihcvXmDHjh3o2LGj+gIlSaijfjg4OKT7DAF8fLN18ODB6N+/v5qjp7ygrr8hyV8y37lzJ9W25LzkZwgqWNRVRz58+IDo6Gh8+PAh1bZXr14BABQKxdeESgVYrjyrCpQtCQkJQrly5QSFQiEEBASI+dHR0YKTk5OgpaUl3L9/X7oASTILFy4UAAgbNmxIc/usWbMEAMLUqVPTzJ81a1YeREl5KSkpSfDw8BAACN27dxcSEhIyLM86UrQEBAQIb968SZUfEREhVKtWTQAg+Pj4iPmsH5RswIABAgDh4sWLqbaxnhQtt2/fFiIjI1Plnzt3TtDR0REUCoUQHBws5rN+5L7sthVevXol3L17V3j16pXKcU6fPi0AEFxdXYX4+Hgx/+TJk4JMJhMaNWqU69dCuUNddSQoKEiwsbERtLS0hD179uRV+JTL1FU/0gNAqFChgrrDpjykrjry+PFjQaFQCObm5sLz589VjpPcFjl58mSuXw+pn7rqSMuWLQUAwowZM1Ty4+LixG0rV67M1Wuh3JfZd5l5+azKToocOH36tCCXywUDAwNh6NChwuTJk4WyZcsKAIR58+ZJHR7loXXr1gkDBgwQBgwYINSoUUMAIDRs2FDM27dvn1j23bt34n/2zZs3F77//nuhdevWAgChWrVqwrt376S7EMoVnp6eAgDBwMBA+OGHHwRPT89UPykfGlhHipbx48cL+vr6Qrt27YTRo0cLU6dOFXr27CkYGBgIAISuXbsKSUlJYnnWD0qWUScF60nR4unpKejq6grt2rUTxowZI0yePFlo2bKlIJPJBE1NTWHdunUq5Vk/8kZ22grJzwqenp6pjjNkyBABgODo6Ch8++23Qv/+/QWFQiEYGhoKt2/fzqOrodygjjpiY2MjABDq1auX5jNmWnWKCgZ1/Q1JCzspCgd11ZFffvlFACCUKFFCGDJkiDB69GjB1tZWACAMGzYsj66GcoM66khAQIBQrFgxAYBQp04dYeLEicLIkSOFcuXKCQCEmjVrCrGxsXl4VaQu2fkuMy+fVdlJkUOXL18WWrVqJRgaGgq6urpCrVq1hM2bN0sdFuWx5C+K0vv58pf4zZs3wsSJE4UyZcoIcrlcKFOmjDBx4sQ036amgi+z+pFWbzXrSNFx7tw5wcPDQ6hYsaJQvHhxQUtLSzA3NxdatWolbN26VVAqlan2Yf0gQci4k0IQWE+KEj8/P6FHjx5C+fLlhWLFiglyuVwoXbq00KtXL+Hy5ctp7sP6kTey2lbIqOGXlJQk/PLLL4KTk5OgUCiEEiVKCN26deOo7ULia+tIZs+YnDShYFPH35C0sJOi8FBXHTl48KDg6uoqGBgYCDo6OkLNmjWF33//PZejp7ygjjry4MEDYeDAgYK1tbUgl8sFXV1doUqVKsLs2bOFmJiYPLgKyg3Z+S4zL59VZYIgCFmYFYqIiIiIiIiIiIiIiEituHA2ERERERERERERERFJgp0UREREREREREREREQkCXZSEBERERERERERERGRJNhJQUREREREREREREREkmAnBRERERERERERERERSYKdFEREREREREREREREJAl2UhARERERERERERERkSTYSUFERERERERERERERJJgJwUREREREREREREREUmCnRREVOB5eHhAJpNBJpPB3d1d6nAom7y8vMTPz9bWVupwsm3jxo1i/DKZLFfP5efnp3KuJ0+e5GqMKfdR52eU0+tQJ1tbW/H8Xl5eOTrGmzdvcu0eEREREeUH+eG5TR3Y5si6vG5zEBEB7KQgKpS+fKjYuHGj1CHlGDsgCraC3hggIiIiIsqpGzduYNSoUahSpQqMjIygra2NkiVLokmTJvjpp58QFRUldYiFAtscREQFn5bUARARfa1evXqhcuXKAIAyZcpIHA1R4TRixAjY2dnB0NDwq49lZ2eHJUuWiGkTE5OvPqYUdHV1xevw9/fHjh07JI6IiIiI8oPExERMnjwZv/zyS6ptYWFhCAsLg6+vL3788Uds2bIFLVq0kCBKIiKi/IOdFERU4LVq1QqtWrWSOow89fbtWxQrVkxt5XIzBinExcVBU1MTcrlc6lAKjZ49e6ptNFOZMmUwZcoUtRxLSgqFQryOjRs3spOCiIiIAABjx47FmjVrxLSVlRV69OiBEiVK4NatW9i1axeSkpIQHh6O9u3b4/Tp02jYsKGEEaeWn5/18wu2OYiI1IfTPRGR6MWLF5gyZQoqV64MAwMD6OjowM7ODoMHD8bNmzfT3EcQBOzYsQPt2rVDqVKloK2tjRIlSqB27dr47rvvVMouWLAAHTt2hL29PUxMTCCXy2FsbIy6detiwYIFiImJEcsmz2e5adMmMe/MmTMq01j5+fkByHxKqOxe15fHCwkJweDBg1GyZEno6OigatWq2LNnT7bv78OHDzF69GhUrFgRenp60NPTQ5UqVeDp6ZnmUO8v58w/deoU3NzcULx4cRQvXhxA6qm9AgMDMW/ePDg4OEBbWxtjx44Vj5eYmIg//vgDTZo0QYkSJSCXy2FmZoYWLVpgy5YtEARB5fzZOfaXkvedPXu2mBccHJylacjevXuHqVOnwtraGgqFAhUqVMDq1atTlXN3dxeP5eHhgYCAALRp0wbGxsbQ1dXFixcvcnzvg4ODMXz4cNjb20NXVxc6OjqwsrJCw4YNMWnSJNy9ezfda09ISMDChQthb28PhUIBW1tbzJkzB0qlMlXZ7H4mmQkODsY333wDExMT6Ovro1GjRjh58mS2jpEdT548SfU76ePjg5o1a0JXVxfm5uYYMmQIwsLCVPZLb57b1atXi3na2tq4ceOGuM/du3eho6Mjbl+6dKnKMbP7GWfk4MGDaNWqFUqWLAm5XI7ixYvDzs4OnTp1wsKFC9P8LImIiIgA4OLFiyodFLVq1cLdu3fx888/44cffsC2bdtw8uRJaGpqAgA+fPiA4cOHQ6lUQqlUwsbGRnzemTdvXqrjjx8/XtxepUoVlW250d7IiL+/P0aMGIE6derAysoKurq60NXVha2tLXr16oW///471T5fTs305s0bjB8/HqVLl4ZCoYCTkxNWrVqVpedgtjmKRpuDiIoIgYgKHV9fXwGA+LNhw4ZM9zlz5oxgZGSksl/KH7lcLmzcuFFln/fv3wutWrVKd58v/8To6+tnWLZKlSrC27dvBUEQhA0bNmRYFoDg6+srCIIgDBgwQMxzc3P76utKebxy5coJFhYWqfaTyWTCsWPHsvyZ7NmzR9DV1U03Djs7OyE4OFhlHxsbG3F7vXr1BE1NzVT39svPumHDhirpAQMGCIIgCO/evRMaNWqU4f1s166d8OHDB/H8WT12Wr7cN62f5Hrp6ekp5pmZmQnVqlVLs/zvv/+ucg43NzdxW/Xq1QU9PT2V8kFBQTm69//9959gZmaWYeyrV68Wy39ZV1u0aJHmPtOnT1eJXx2fSfI1CoIgBAUFpVtXW7dune7vZUbS+n1LKSgoSKVMkyZN0ryO8uXLC+Hh4Vm6jo4dO4r51apVEz58+CAkJiYKderUEfObN28uKJVKcZ+v/f3y9PRM9/NM6yc2NjbVvUi5n42NTZbvMRERERUuKdsSAITTp0+nWa5Pnz4q5fz8/ARBEISZM2eKeY6Ojir7JCUlCaVKlRK3//zzz+K2vGpvpHxuW7JkSYbPTDKZLFVb9Mtn/8qVK6e574QJEzK912xzFI42BxGRIAgCp3siIrx58wadO3fGmzdvAAD6+voYNGgQdHV14ePjg5cvXyIhIQFDhgxBjRo1xDd2Jk2ahKNHj4rHsbW1RceOHVGsWDH8+++/+PPPP1XOY21tjcqVK8Pa2hrGxsYQBAFBQUHYsWMHYmJicPPmTaxatQpTp05F7dq1sWTJEuzYsQP+/v4AgHLlymHkyJHi8ezs7HLlulJ6/Pgx9PT0MHbsWCiVSqxZswZJSUkQBAFLly7N0vyxjx8/Rp8+fRAXFwcAcHZ2RqdOnfDhwwf4+PjgxYsXePToEb755hucP38+zWNcunQJxYoVQ58+fWBpaSneky+dP38ezs7OaNu2LZRKpbh+wNixY3H27FmxXOvWrVG7dm2cPXtWHJFy+PBhzJw5E4sWLcrWsdOSvObA8ePHceLECQCAsbExpk+fLpapXbt2qv1evXqF169fY9CgQShRogR+++03vH//HgDw008/YejQoWmeLyAgAHK5HB4eHrCzs8Pt27chl8tzdO/37NmDV69eiTEPHDgQJUqUQEhICO7du4dz586le90AcPz4cXTv3h3ly5eHt7e3OIpg5cqV8PT0hLa2NgD1fCYpjRkzBqGhoWK6ffv2qF69Ov766y/89ddfme6vDqdPn0bjxo3h6uqK8+fP49SpUwA+vlX23Xff4Y8//sj0GN7e3rh69SpCQkJw/fp1zJ8/HwqFAleuXAEAmJqaYtOmTZDJZADU8/uVUso36GrXro127dohMTERz549w+XLlzN8o42IiIgo5bOiiYkJGjdunGa57t27Y8uWLSr7ubm5wcPDA/PmzYMgCLhz5w5u3LiBqlWrAvg4cuDly5cAALlcjr59+wLI2/ZGSjo6Oqhfvz6qVauGEiVKQF9fH1FRUTh16hSuXr0KQRAwefJk9OzZE7q6uqn2f/XqFaKjozFixAgYGRlh8+bNeP78OQBg+fLl6NKlC1xdXdM9P9scRbPNQUSFlLR9JESUG7I7kmLZsmUq5VOOEHj06JEgl8vFbUOGDBEEQRAiIiIELS0tMb9mzZrCu3fvVI776NGjVOd68+aNcOTIEWHNmjXC0qVLhSVLlqi82dGkSROV8hmNksisTE6u68vjARAOHz4sbpswYYKYb2JikuF9TTZx4kRxnypVqgjx8fHitnv37qmc6/z58+K2lG82aWlpCf/++2+qY3/5Wbu6uqocXxAEITw8XOWtqG+++UbcplQqhaZNm4rb9PX1hbi4uCwfOzMp31hK7+3ylGUACL/++qu4bfny5SrboqOjxW0p32oCIBw5ciTVsXNy73/++Wcxb/jw4amO+e7dOyE0NFRMf/lW05QpU8Rt+/fvV9mW/Bmq6zNJfqspJCREkMlkYn7fvn3F43348EFwcnLK0VtNKffJykiKFi1aiCMclEqlyhteCoVCiImJyfA6kp08eVK8HrlcLmhra4tlDx48qFJWHb9fKUdSODs7i/kXL15M85qTkpJS5XMkBREREQmCoPI2fbVq1dItFxAQoPKcMmrUKHFbyufc7777TswfMmSImN+5c2cxPy/bG18+twmCINy4cUPYvHmzsGLFCmHJkiXCvHnzVPY5e/asWPbLZ/8tW7aI24KCglTaaP369cvgTn/GNkfBbnMQEQmCIHBNCiLChQsXxH+bm5urjA4oV64cXFxcUpW9fPkyEhMTxfzvvvsO+vr6KsctV66c+G+lUompU6fC3Nwcbdq0wYgRIzB58mR8++23Km92JL85I9V1fcnKygpt27YV0xUqVBD/HRkZmaU4Ur6tdPPmTSgUCnFe04oVK6Ybc0pt27ZNc6THlyZNmiS+NZPs8uXLSEpKEtP9+vUT/y2TydC/f38xHRMTg3///TfLx1Y3TU1NDB48WEynvN9A+ve8atWqaN26dar8nNz7hg0bim/p//7776hZsyb69euHefPm4ejRo9DS0kLJkiXTvYbhw4dnGr+6PpNk//zzj8pcsn369BH/LZfL0aNHjwz3V5e+ffuK904mk6nEER8fj1u3bmXpOE2bNhUXpE5ISMCHDx8AAKNGjUL79u1Vyqrj9yullG/rNW/eHC1atMDo0aPx22+/4ebNm7C1tYWGBh+fiIiIKHPJz0XZNXDgQPHf27dvhyAISEhIUFkXL2WZvGxvpHTt2jVUrlwZVatWRd++fTF+/Hh8++23mDFjhkq59Np4crkcPXv2FNO2trYqbbSsjObICbY5Cnabg4gKJ7ayiUjlIczc3DzV9pQPR8llX79+rVLG1tY2w3P88ssvWLJkifhlY3ri4+MzCzfLcnJdX7KxsVFJKxQK8d8pH9Ay8uW9ykjykN8vOTg4ZGn/tMp9eW1f3osvH37TuxdZjeFrJC9Onizl/QaQ7oLF6cWWk3tfp04d/PzzzzAwMIAgCLh27Ro2b96MmTNnonXr1ihdurQ4NDotKetMevGr6zNJljylWVaPl1u+9jpSGj16dKrOgDFjxqQqp47fr5QWLFggNj7fvXuHEydOYNWqVRgzZgycnZ3h7u4uTgdARERE9KVSpUqJ/w4ODk633JfbUu7XrVs3GBgYiOUuXLiAo0ePis9SFhYWKl+W52V7I1lsbCzatWuH27dvZ1o2vTZeiRIlxAXEk2Wljfa12Ob4qKC2OYiocOKaFEQEY2Nj8d/Jc1mm9N9//6Uqa2JiolLmyZMnac73mWzHjh3ivytXroytW7eiYsWKkMvlmDp1KpYsWZLj+NOTk+v6klwuV0nn5G2olMdOfssoPendQz09vSydK61yX17bl/ci5X1Iq3x2Y/gaOb3f6cWW03s/YcIEDBs2DJcuXcLt27cRGBiIo0ePIjAwEOHh4fDw8MCTJ08yvYb04lfXZ5LMyMgoW8fLLZmd98s40yMIAoYMGZKqgTh06FCcOXNGpTGrjt+vlIoXL44jR47g+fPnuHTpEh48eIA7d+5g3759eP/+Pc6cOYPFixfDy8srS9dCRERERYurqyseP34M4OOX16dPn0aTJk1Sldu5c2eq/ZLp6+ujR48eWL9+PQBg27ZtKl+E9+vXD1pan7/Oycv2RrKzZ8+K62MAwJIlSzB48GAYGxvj/fv3qUbZpyUiIgJJSUkqz3Ypnx+z+uyYXWxzfFRQ2xxEVDixk4KI0KBBA+zatQvAxweN48ePi1MjPX78GH///bdKWQCoW7cutLS0xCmflixZgnbt2qksiBYcHCy+4RERESHmN27cWBxKHBsbi4MHD6YbW8qHr+y+vZyT68oNDRo0wNWrVwEAL1++RN++fWFhYaFSJi4uDrt27YKbm5vaz1+nTh1oamqKQ319fHzEN68EQYCPj49YVl9fH87Ozmo799d8fuqQk3sfEhICTU1NlCxZEk2aNBEblQEBAahRowaAj3U7IiICJUqUyFFc6v5MatSoAZlMJo7u2bJlC1q1agXg43RJXzaCc8vmzZvFKZ8EQVBZDFJbWzvLUwgsXboUJ0+eBPCxQ1RLSwthYWE4f/485s2bB09PT7Gsun+/bt26hQoVKqB06dLo1q2bmD9+/Hj88ssvAD4OdSciIiJKy7Bhw7Bp0yYx/d133+H06dMoVqyYmOfn56fyEpejo2OqBaIHDhwodlLs3LlT5Vk65VRPgDTtjZTtOwAYNGiQ+CX39u3bs3SMhIQE7NixA7179wbw8cW3lG20WrVqZek4bHOkrbC2OYiocGInBVERMHv2bPz666+p8itUqIAtW7ZgwIABmDt3rvh2TpcuXTBo0CDo6urCx8cHCQkJAAAtLS2MHTsWwMcvDgcPHoy1a9cCAK5evQonJyd06tQJxYsXx507d3DgwAFxaG+FChUQGBgIAFi3bh1kMhmKFy+OXbt24f79++nGbmVlJf77n3/+wfjx41GmTBloa2tj3LhxGV53Tq4rN4wdOxZr1qxBfHw8wsLCULVqVfTo0QOWlpaIjo7GzZs3cebMGbx7905lnlB1MTU1Rb9+/bBx40YAH9/EevPmDerUqYMzZ86oDCMeNWpUquHCXyPl5/fq1SsMHDgQjo6OkMlkGD16tEqnVm7Iyb0/e/Ys+vTpAxcXF1SqVAmWlpZISkrC3r17xeNqa2t/Vezq/kwsLS3RunVrHDlyBMDHzoLo6GhUq1YNf/31V5aG4avD8ePH0bRpUzRq1Ah///03Tp06JW7r06dPlt7QCwgIwA8//CCmV61aBR0dHXTq1AkAMHfuXDRv3lzsWFT379eUKVNw5coVNG3aFGXKlIGZmRlCQkKwYcMGsUxuvdVHREREBV+DBg0wfPhwsZ3k7++PSpUqoUePHjA1NcXNmzexe/du8YtjbW1t/P7776mmuXRxcYG9vT0CAwNVpmiqV68eKlWqpFJWivbGl+sgtGnTBm3btkVgYCC2bt2a5eMMGjQI586dg5GRETZv3iy20YCPo2izgm2OtBXWNgcRFVKSLNdNRLnK19dXAJDpT9WqVcV9Tp8+LRgaGqZbVktLS/D29lY5z/v374WWLVtmeI5k586dE7S0tFJtNzAwELp06SKmbWxsVM4REBAgaGhopNpPX19fLDNgwAAx383NTWX/nFxXRsfbsGFDmteXmd27dwu6urqZfiYp2djYiPmenp5pHvfLzzooKCjNctHR0ULDhg0zPHfr1q2F+Pj4bB87Iy9fvhT09PTSPN+rV68EQRAET0/PdD//jGJwc3MT8wcMGJBuDNm999u2bcu07KRJk8TyGdWJoKAglW2+vr7iNnV/Jo8fPxbMzc3TPE7Ke5Wdepte7OldX9u2bdM8f7ly5cTPO6PriImJESpWrCjm9+jRQ9wn5e9l2bJlhaioqBx/xoKQ/u9XZn/TdHR0hMuXL6e6FynrwZf1mIiIiIqWhIQEYcyYMZk+m5QoUUI4duxYuseZP39+qn1+//33NMtK0d5o1apVmudI+dwGQNiwYYO4T8pn/5IlSwo1a9ZM8xhjx47N8v1mm6NgtzmIiARBELhwNhEB+DgF082bNzFhwgRUqlQJurq6UCgUsLW1hYeHB/z9/TFo0CCVfXR1dfHXX39h27ZtaNOmDUqWLAm5XA5DQ0NUq1YNkydPFsu6uLjg2LFjaNCgARQKBQwNDdGmTRtcuHAhwylgqlWrhm3btqFGjRoqi5vl5nXlhq5du+LmzZsYN24cHB0doa+vDx0dHZQrVw6NGzfGwoULce/evVw7f7FixeDn54e1a9fCzc0NxsbG0NLSQokSJdC0aVNs2rQJhw8fhra2tlrPa2FhgUOHDqFhw4ZZmpc2N2T33ru4uGD+/Plo27Yt7OzsUKxYMWhpacHMzAxNmzbFxo0b8dNPP311XOr+TMqWLYtLly6hR48eMDIygq6uLurXr49Dhw7Bw8Pjq+PNiilTpmDbtm2oWbMmdHR0YGpqikGDBuHChQswNTXNdP+JEyeKn4WFhQVWrVolbvvll19gbW0NAAgKCsKoUaPEber8/fr2228xfvx41KtXD1ZWVtDW1oZCoUC5cuUwYMAAXLlyBXXq1MnObSEiIqIiRktLCytXrkRAQABGjhwJR0dHlWdKd3d3LF68GI8ePRKno03LgAEDVEZY6OrqomfPnmmWlaK9sWfPHkyYMAGlSpWCtrY2ypcvjwULFsDb2ztL++vo6MDX1xcTJ04Un7sqVqyIlStXYsWKFVmOg22O9BXGNgcRFU4yQfg0mRwRERFRCikX4RsxYgTs7OxgaGgoDr1/8uQJypYtK5bx9fWFu7t7Xocpmfj4eKxcuRLAx6kckueWtrGxSXeRQyIiIqKizMvLC7NnzwbAZyYiIvqMa1IQERFRptasWQPgY2Myq/MDF3axsbH49ttvpQ6DiIiIiIiIqEDjdE9ERERERERERERERCQJjqQgIiKiNHFGyIwZGRnxHhERERERERF9Ja5JQUREREREREREREREkuB0T0REREREREREREREJAl2UhARERERERERERERkSTYSUFERERERERERERERJJgJwUREREREREREREREUmCnRRERERERERERERERCQJdlIQEREREREREREREZEk2ElBRERERERERERERESSYCcFERERERERERERERFJgp0UREREREREREREREQkif8Ds8NVOPYKryEAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1600x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "精确度和成功率详细结果:\n", "============================================================\n", "模型\t\t精确度AUC\t成功率AUC\n", "------------------------------------------------------------\n", "fe1081_ep71    \t78.7\t\t60.9\n", "fe1082_ep80    \t78.6\t\t60.7\n", "fe108_ep59     \t77.9\t\t60.2\n", "============================================================\n"]}], "source": ["# 绘制论文风格的精确度和成功率对比图\n", "plt.style.use('default')  # 使用默认样式以获得更好的论文风格\n", "plt.rcParams['font.size'] = 14\n", "plt.rcParams['axes.linewidth'] = 1.2\n", "plt.rcParams['lines.linewidth'] = 2\n", "\n", "# 定义颜色方案\n", "colors = ['red', 'green', 'blue', 'orange', 'purple', 'brown', 'pink', 'gray']\n", "model_colors = {model: colors[i % len(colors)] for i, model in enumerate(sorted(model_names))}\n", "\n", "# 创建图形\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))\n", "\n", "# 1. 精确度图 (Precision plot)\n", "ax1.set_xlim(0, 50)\n", "ax1.set_ylim(0, 100)\n", "ax1.set_xlabel('Location error threshold [pixels]', fontsize=14, fontweight='bold')\n", "ax1.set_ylabel('Distance Precision [%]', fontsize=14, fontweight='bold')\n", "ax1.set_title('Precision plot', fontsize=16, fontweight='bold')\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# 绘制精确度曲线\n", "error_thresholds = np.linspace(0, 50, 51)\n", "legend_labels = []\n", "\n", "for model_name in sorted(model_names):\n", "    if model_name in all_models_metrics:\n", "        metrics = all_models_metrics[model_name]\n", "        all_center_errors = all_models_center_errors[model_name]\n", "        \n", "        precision_rates = []\n", "        for threshold in error_thresholds:\n", "            precision_rate = np.mean(all_center_errors <= threshold) * 100  # 转换为百分比\n", "            precision_rates.append(precision_rate)\n", "        \n", "        # 计算AUC分数（显示在图例中）\n", "        precision_auc_score = metrics['precision_auc'] * 100\n", "        \n", "        ax1.plot(error_thresholds, precision_rates, \n", "                color=model_colors[model_name], linewidth=2.5, \n", "                label=f'{model_name}')\n", "        \n", "        legend_labels.append(f'{model_name} [{precision_auc_score:.1f}]')\n", "\n", "# 设置精确度图的图例\n", "ax1.legend(legend_labels, loc='lower right', fontsize=12, framealpha=0.9)\n", "\n", "# 2. 成功率图 (Success plot)\n", "ax2.set_xlim(0.0, 1.0)\n", "ax2.set_ylim(0, 100)\n", "ax2.set_xlabel('Overlap threshold', fontsize=14, fontweight='bold')\n", "ax2.set_ylabel('Overlap Precision [%]', fontsize=14, fontweight='bold')\n", "ax2.set_title('Success plot', fontsize=16, fontweight='bold')\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# 绘制成功率曲线\n", "legend_labels = []\n", "\n", "for model_name in sorted(model_names):\n", "    if model_name in all_models_metrics:\n", "        metrics = all_models_metrics[model_name]\n", "        success_rates = metrics['success_rates'] * 100  # 转换为百分比\n", "        \n", "        # AUC分数（显示在图例中）\n", "        auc_score = metrics['auc_score'] * 100\n", "        \n", "        ax2.plot(overlap_thresholds, success_rates, \n", "                color=model_colors[model_name], linewidth=2.5,\n", "                label=f'{model_name}')\n", "        \n", "        legend_labels.append(f'{model_name} [{auc_score:.1f}]')\n", "\n", "# 设置成功率图的图例\n", "ax2.legend(legend_labels, loc='upper right', fontsize=12, framealpha=0.9)\n", "\n", "# 添加总标题\n", "fig.suptitle('Quantitative results on FE108 dataset', fontsize=18, fontweight='bold', y=0.95)\n", "\n", "plt.tight_layout()\n", "plt.subplots_adjust(top=0.88)  # 为总标题留出空间\n", "plt.show()\n", "\n", "# 打印图例中的数值\n", "print(\"\\n精确度和成功率详细结果:\")\n", "print(\"=\" * 60)\n", "print(\"模型\\t\\t精确度AUC\\t成功率AUC\")\n", "print(\"-\" * 60)\n", "for model_name in sorted(model_names):\n", "    if model_name in all_models_metrics:\n", "        metrics = all_models_metrics[model_name]\n", "        precision_auc = metrics['precision_auc'] * 100\n", "        success_auc = metrics['auc_score'] * 100\n", "        print(f\"{model_name:<15}\\t{precision_auc:.1f}\\t\\t{success_auc:.1f}\")\n", "print(\"=\" * 60)"]}, {"cell_type": "code", "execution_count": 11, "id": "15d4557c", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1200 with 5 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "🏆 各指标性能排名:\n", "================================================================================\n", "\n", "AUC 排名:\n", "  1. fe1081_ep71: 0.6089\n", "  2. fe1082_ep80: 0.6069\n", "  3. fe108_ep59: 0.6017\n", "\n", "OP50 排名:\n", "  1. fe1081_ep71: 0.7816\n", "  2. fe1082_ep80: 0.7765\n", "  3. fe108_ep59: 0.7749\n", "\n", "OP75 排名:\n", "  1. fe1081_ep71: 0.3440\n", "  2. fe1082_ep80: 0.3384\n", "  3. fe108_ep59: 0.3350\n", "\n", "RSR 排名:\n", "  1. fe1081_ep71: 0.7701\n", "  2. fe1082_ep80: 0.7671\n", "  3. fe108_ep59: 0.7620\n", "\n", "RPR 排名:\n", "  1. fe1081_ep71: 0.8687\n", "  2. fe1082_ep80: 0.8674\n", "  3. fe108_ep59: 0.8590\n", "================================================================================\n"]}], "source": ["# 绘制多模型性能对比图\n", "fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "\n", "# 1. 主要指标对比柱状图\n", "models = sorted(model_names)\n", "metrics_names = ['AUC', 'OP50', 'OP75', 'RSR', 'RPR']\n", "metrics_data = {metric: [] for metric in metrics_names}\n", "\n", "for model_name in models:\n", "    if model_name in all_models_metrics:\n", "        metrics = all_models_metrics[model_name]\n", "        metrics_data['AUC'].append(metrics['auc_score'])\n", "        metrics_data['OP50'].append(metrics['mean_op50'])\n", "        metrics_data['OP75'].append(metrics['mean_op75'])\n", "        metrics_data['RSR'].append(metrics['rsr'])\n", "        metrics_data['RPR'].append(metrics['rpr'])\n", "\n", "x = np.arange(len(models))\n", "width = 0.15\n", "\n", "for i, metric in enumerate(metrics_names):\n", "    offset = (i - 2) * width\n", "    bars = ax1.bar(x + offset, metrics_data[metric], width, \n", "                   label=metric, alpha=0.8, color=colors[i])\n", "    \n", "    # 在柱子上添加数值标签\n", "    for bar, value in zip(bars, metrics_data[metric]):\n", "        if value > 0:  # 只显示有效值\n", "            ax1.annotate(f'{value:.3f}', \n", "                        xy=(bar.get_x() + bar.get_width() / 2, bar.get_height()),\n", "                        xytext=(0, 3), textcoords=\"offset points\",\n", "                        ha='center', va='bottom', fontsize=8, rotation=90)\n", "\n", "ax1.set_xlabel('Models')\n", "ax1.set_ylabel('Score')\n", "ax1.set_title('Performance Metrics Comparison')\n", "ax1.set_xticks(x)\n", "ax1.set_xticklabels(models, rotation=45)\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "ax1.set_ylim(0, 1)\n", "\n", "# 2. IoU分布对比\n", "for model_name in models:\n", "    if model_name in all_models_ious:\n", "        all_ious = all_models_ious[model_name]\n", "        ax2.hist(all_ious, bins=30, alpha=0.6, \n", "                label=f'{model_name} (μ={np.mean(all_ious):.3f})',\n", "                color=model_colors[model_name])\n", "\n", "ax2.set_xlabel('IoU')\n", "ax2.set_ylabel('Frequency')\n", "ax2.set_title('IoU Distribution Comparison')\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# 3. 中心误差分布对比\n", "for model_name in models:\n", "    if model_name in all_models_center_errors:\n", "        center_errors = all_models_center_errors[model_name]\n", "        ax3.hist(center_errors, bins=30, alpha=0.6,\n", "                label=f'{model_name} (μ={np.mean(center_errors):.1f}px)',\n", "                color=model_colors[model_name])\n", "\n", "ax3.set_xlabel('Center Error (pixels)')\n", "ax3.set_ylabel('Frequency')\n", "ax3.set_title('Center Error Distribution Comparison')\n", "ax3.legend()\n", "ax3.grid(True, alpha=0.3)\n", "\n", "# 4. 综合性能雷达图\n", "angles = np.linspace(0, 2 * np.pi, len(metrics_names), endpoint=False).tolist()\n", "angles += angles[:1]  # 闭合图形\n", "\n", "ax4 = plt.subplot(224, projection='polar')\n", "\n", "for model_name in models:\n", "    if model_name in all_models_metrics:\n", "        metrics = all_models_metrics[model_name]\n", "        values = [metrics['auc_score'], metrics['mean_op50'], metrics['mean_op75'], \n", "                 metrics['rsr'], metrics['rpr']]\n", "        values += values[:1]  # 闭合图形\n", "        \n", "        ax4.plot(angles, values, 'o-', linewidth=2, \n", "                label=model_name, color=model_colors[model_name])\n", "        ax4.fill(angles, values, alpha=0.1, color=model_colors[model_name])\n", "\n", "ax4.set_xticks(angles[:-1])\n", "ax4.set_xticklabels(metrics_names)\n", "ax4.set_ylim(0, 1)\n", "ax4.set_title('Multi-Model Performance Radar Chart', pad=20)\n", "ax4.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))\n", "ax4.grid(True)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 性能排名\n", "print(\"\\n🏆 各指标性能排名:\")\n", "print(\"=\" * 80)\n", "for metric in metrics_names:\n", "    print(f\"\\n{metric} 排名:\")\n", "    metric_key_map = {\n", "        'AUC': 'auc_score',\n", "        'OP50': 'mean_op50', \n", "        'OP75': 'mean_op75',\n", "        'RSR': 'rsr',\n", "        'RPR': 'rpr'\n", "    }\n", "    \n", "    model_scores = [(model, all_models_metrics[model][metric_key_map[metric]]) \n", "                   for model in models if model in all_models_metrics]\n", "    model_scores.sort(key=lambda x: x[1], reverse=True)\n", "    \n", "    for i, (model, score) in enumerate(model_scores, 1):\n", "        print(f\"  {i}. {model}: {score:.4f}\")\n", "print(\"=\" * 80)"]}, {"cell_type": "code", "execution_count": 12, "id": "97bcc942", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["结果已保存到: ../results_analysis\n", "  - 多模型对比: multi_model_comparison.csv\n", "  - 详细序列对比: detailed_sequence_comparison.csv\n", "  - fe1081_ep71 详细结果: fe1081_ep71_detailed_results.csv\n", "  - fe1082_ep80 详细结果: fe1082_ep80_detailed_results.csv\n", "  - fe108_ep59 详细结果: fe108_ep59_detailed_results.csv\n", "\n", "====================================================================================================\n", "多模型评估最终总结\n", "====================================================================================================\n", "评估数据集: FE108\n", "评估序列数: 32\n", "评估模型数: 3\n", "\n", "🏆 最佳AUC: fe1081_ep71 (0.6089)\n", "🏆 最佳OP50: fe1081_ep71 (0.7816)\n", "🏆 最佳OP75: fe1081_ep71 (0.3440)\n", "🏆 最佳RSR: fe1081_ep71 (0.7701)\n", "🏆 最佳RPR: fe1081_ep71 (0.8687)\n", "\n", "详细性能对比:\n", "      Model    AUC   OP50   OP75    RSR    RPR\n", "fe1081_ep71 0.6089 0.7816 0.3440 0.7701 0.8687\n", "fe1082_ep80 0.6069 0.7765 0.3384 0.7671 0.8674\n", " fe108_ep59 0.6017 0.7749 0.3350 0.7620 0.8590\n", "====================================================================================================\n"]}], "source": ["# 保存结果到CSV文件\n", "output_dir = Path('../results_analysis')\n", "output_dir.mkdir(exist_ok=True)\n", "\n", "# 保存多模型对比结果\n", "df_comparison.to_csv(output_dir / 'multi_model_comparison.csv', index=False)\n", "\n", "# 保存详细序列对比结果\n", "if 'df_detailed' in locals():\n", "    df_detailed.to_csv(output_dir / 'detailed_sequence_comparison.csv', index=False)\n", "\n", "# 为每个模型单独保存详细结果\n", "for model_name in model_names:\n", "    if model_name in all_models_results:\n", "        sequence_results = all_models_results[model_name]\n", "        df_model = pd.DataFrame([\n", "            {\n", "                'Sequence': r['sequence'],\n", "                'Frames': r['num_frames'],\n", "                'Mean IoU': r['mean_iou'],\n", "                'OP50': r['op50'],\n", "                'OP75': r['op75'],\n", "                'Mean Center Error': r['mean_center_error']\n", "            }\n", "            for r in sequence_results\n", "        ])\n", "        \n", "        # 添加平均值行\n", "        metrics = all_models_metrics[model_name]\n", "        avg_row = {\n", "            'Sequence': 'AVERAGE',\n", "            'Frames': metrics['total_frames'],\n", "            'Mean IoU': metrics['mean_iou'],\n", "            'OP50': metrics['mean_op50'],\n", "            'OP75': metrics['mean_op75'],\n", "            'Mean Center Error': metrics['mean_center_error']\n", "        }\n", "        df_model = pd.concat([df_model, pd.DataFrame([avg_row])], ignore_index=True)\n", "        df_model.to_csv(output_dir / f'{model_name}_detailed_results.csv', index=False)\n", "\n", "print(f\"结果已保存到: {output_dir}\")\n", "print(f\"  - 多模型对比: multi_model_comparison.csv\")\n", "print(f\"  - 详细序列对比: detailed_sequence_comparison.csv\")\n", "for model_name in model_names:\n", "    if model_name in all_models_results:\n", "        print(f\"  - {model_name} 详细结果: {model_name}_detailed_results.csv\")\n", "\n", "# 打印最终总结\n", "print(\"\\n\" + \"=\" * 100)\n", "print(\"多模型评估最终总结\")\n", "print(\"=\" * 100)\n", "print(f\"评估数据集: FE108\")\n", "print(f\"评估序列数: {len(sequences)}\")\n", "print(f\"评估模型数: {len([m for m in model_names if m in all_models_metrics])}\")\n", "print()\n", "\n", "# 显示最佳性能模型\n", "best_models = {}\n", "for metric in ['AUC', 'OP50', 'OP75', 'RSR', 'RPR']:\n", "    best_row = df_comparison.loc[df_comparison[metric].idxmax()]\n", "    best_models[metric] = (best_row['Model'], best_row[metric])\n", "    print(f\"🏆 最佳{metric}: {best_row['Model']} ({best_row[metric]:.4f})\")\n", "\n", "print()\n", "print(\"详细性能对比:\")\n", "print(df_comparison[['Model', 'AUC', 'OP50', 'OP75', 'RSR', 'RPR']].to_string(index=False, float_format='%.4f'))\n", "print(\"=\" * 100)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}