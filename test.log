nohup: 忽略输入
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00141_tank_outdoor2——————————————
00141_tank_outdoor2 , fps:1.816706941452763
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00147_tank_outdoor2——————————————
00147_tank_outdoor2 , fps:2.480979258896621
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00197_driving_outdoor3——————————————
00197_driving_outdoor3 , fps:4.723436342184627
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00236_tennis_outdoor4——————————————
00236_tennis_outdoor4 , fps:2.0981719541215003
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00404_UAV_outdoor6——————————————
00404_UAV_outdoor6 , fps:1.5614491973669027
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00406_UAV_outdoor6——————————————
00406_UAV_outdoor6 , fps:1.6991002953136116
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00408_UAV_outdoor6——————————————
00408_UAV_outdoor6 , fps:3.2239163809994658
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00410_UAV_outdoor6——————————————
00410_UAV_outdoor6 , fps:1.834676526765491
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00449_UAV_outdoor6——————————————
00449_UAV_outdoor6 , fps:2.0385905969034552
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00451_UAV_outdoor6——————————————
00451_UAV_outdoor6 , fps:2.527638160820425
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00453_UAV_outdoor6——————————————
00453_UAV_outdoor6 , fps:1.7323426431307813
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00458_UAV_outdoor6——————————————
00458_UAV_outdoor6 , fps:1.8323447371213195
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00435_UAV_outdoor6——————————————
00435_UAV_outdoor6 , fps:1.6742034276980924
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00437_UAV_outdoor6——————————————
00437_UAV_outdoor6 , fps:1.7278325394428897
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00439_UAV_outdoor6——————————————
00439_UAV_outdoor6 , fps:1.8946968313752606
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00442_UAV_outdoor6——————————————
00442_UAV_outdoor6 , fps:1.8133047484104672
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00421_UAV_outdoor6——————————————
00421_UAV_outdoor6 , fps:1.540653457190563
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00423_UAV_outdoor6——————————————
00423_UAV_outdoor6 , fps:1.7798727403482493
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00425_UAV_outdoor6——————————————
00425_UAV_outdoor6 , fps:1.9449069876826628
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00430_UAV_outdoor6——————————————
00430_UAV_outdoor6 , fps:1.7306935312211753
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00471_UAV_outdoor6——————————————
00471_UAV_outdoor6 , fps:1.5556214757344755
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00473_UAV_outdoor6——————————————
00473_UAV_outdoor6 , fps:1.9396456484595985
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00478_UAV_outdoor6——————————————
00478_UAV_outdoor6 , fps:1.7619444547493048
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00490_UAV_outdoor6——————————————
00490_UAV_outdoor6 , fps:1.7362291293043688
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00345_UAV_outdoor6——————————————
00345_UAV_outdoor6 , fps:1.6223805470494634
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00351_UAV_outdoor6——————————————
00351_UAV_outdoor6 , fps:1.6082120752972564
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00355_UAV_outdoor6——————————————
00355_UAV_outdoor6 , fps:2.5727446204816586
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00370_UAV_outdoor6——————————————
00370_UAV_outdoor6 , fps:1.719068281682493
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00508_person_outdoor6——————————————
00508_person_outdoor6 , fps:1.6187546465200147
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00510_person_outdoor6——————————————
00510_person_outdoor6 , fps:1.6956074699191077
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00511_person_outdoor6——————————————
00511_person_outdoor6 , fps:1.8118538313027115
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00514_person_outdoor6——————————————
00514_person_outdoor6 , fps:1.493644728715883
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00503_UAV_outdoor6——————————————
00503_UAV_outdoor6 , fps:1.967021398818873
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00506_person_outdoor6——————————————
00506_person_outdoor6 , fps:2.435923710328404
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_15_15_36_redcar——————————————
dvSave-2021_02_06_15_15_36_redcar , fps:2.5472933905919777
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_15_17_48_whitecar——————————————
dvSave-2021_02_06_15_17_48_whitecar , fps:2.134407319169851
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00464_UAV_outdoor6——————————————
00464_UAV_outdoor6 , fps:2.523491692592568
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00466_UAV_outdoor6——————————————
00466_UAV_outdoor6 , fps:2.457050338613146
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_08_56_18_windowPattern——————————————
dvSave-2021_02_06_08_56_18_windowPattern , fps:2.088808578701447
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_08_56_40_windowPattern2——————————————
dvSave-2021_02_06_08_56_40_windowPattern2 , fps:2.490891948606565
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00413_UAV_outdoor6——————————————
00413_UAV_outdoor6 , fps:2.0585195715009923
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00416_UAV_outdoor6——————————————
00416_UAV_outdoor6 , fps:2.0638417354211254
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_09_36_15_Pedestrian——————————————
dvSave-2021_02_06_09_36_15_Pedestrian , fps:2.0416525930252316
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_09_36_44_Pedestrian——————————————
dvSave-2021_02_06_09_36_44_Pedestrian , fps:1.8820439131447528
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00432_UAV_outdoor6——————————————
00432_UAV_outdoor6 , fps:2.002795637339304
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00433_UAV_outdoor6——————————————
00433_UAV_outdoor6 , fps:1.8907168117127902
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_17_21_41_personFootball——————————————
dvSave-2021_02_06_17_21_41_personFootball , fps:2.7457892296903754
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_17_23_26_personFootball——————————————
dvSave-2021_02_06_17_23_26_personFootball , fps:3.9381862144050603
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00385_UAV_outdoor6——————————————
00385_UAV_outdoor6 , fps:1.899109283047123
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00398_UAV_outdoor6——————————————
00398_UAV_outdoor6 , fps:2.2942098175919265
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_17_36_49_personBasketball——————————————
dvSave-2021_02_06_17_36_49_personBasketball , fps:2.6196138110848954
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_17_41_45_personBasketball——————————————
dvSave-2021_02_06_17_41_45_personBasketball , fps:3.103569641716271
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00241_tennis_outdoor4——————————————
00241_tennis_outdoor4 , fps:2.303601775868974
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00340_UAV_outdoor6——————————————
00340_UAV_outdoor6 , fps:2.2004742690892893
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_04_20_41_53——————————————
dvSave-2021_02_04_20_41_53 , fps:3.2227211523006414
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_04_20_49_43——————————————
dvSave-2021_02_04_20_49_43 , fps:5.482336002009473
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_09_16_35_car——————————————
dvSave-2021_02_06_09_16_35_car , fps:2.8315548562373065
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_09_21_53_car——————————————
dvSave-2021_02_06_09_21_53_car , fps:2.6645132029906855
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_09_24_26_Pedestrian1——————————————
dvSave-2021_02_06_09_24_26_Pedestrian1 , fps:2.4272253067723257
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_09_35_08_Pedestrian——————————————
dvSave-2021_02_06_09_35_08_Pedestrian , fps:2.770462697763295
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00445_UAV_outdoor6——————————————
00445_UAV_outdoor6 , fps:1.9022846268546387
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: 00447_UAV_outdoor6——————————————
00447_UAV_outdoor6 , fps:2.0649197461563906
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_10_11_59_paperClips——————————————
dvSave-2021_02_06_10_11_59_paperClips , fps:2.2547691705687094
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_10_14_17_paperClip——————————————
dvSave-2021_02_06_10_14_17_paperClip , fps:2.0813701890008014
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_15_18_36_redcar——————————————
dvSave-2021_02_06_15_18_36_redcar , fps:2.454857583733767
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_17_15_20_whitecar——————————————
dvSave-2021_02_06_17_15_20_whitecar , fps:2.579651016940907
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_17_16_26_whitecar——————————————
dvSave-2021_02_06_17_16_26_whitecar , fps:2.4103904451728653
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_17_20_28_personFootball——————————————
dvSave-2021_02_06_17_20_28_personFootball , fps:2.753868601957503
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: UAV_long_001——————————————
UAV_long_001 , fps:3.6503530624099016
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dightNUM_001——————————————
dightNUM_001 , fps:3.38191412483577
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_17_53_39_personFootball——————————————
dvSave-2021_02_06_17_53_39_personFootball , fps:2.878627934362125
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_17_57_54_personFootball——————————————
dvSave-2021_02_06_17_57_54_personFootball , fps:4.737850534286428
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_04_20_56_55——————————————
dvSave-2021_02_04_20_56_55 , fps:2.8863115084871556
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_04_21_18_52——————————————
dvSave-2021_02_04_21_18_52 , fps:2.617021609852329
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_04_21_20_22——————————————
dvSave-2021_02_04_21_20_22 , fps:2.6401661125639317
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_04_21_21_24——————————————
dvSave-2021_02_04_21_21_24 , fps:2.215855003162994
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_17_27_53_personFootball——————————————
dvSave-2021_02_06_17_27_53_personFootball , fps:3.099074075357215
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_17_31_03_personBasketball——————————————
dvSave-2021_02_06_17_31_03_personBasketball , fps:2.3034742906285732
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_17_33_01_personBasketball——————————————
dvSave-2021_02_06_17_33_01_personBasketball , fps:2.815029863399858
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_17_34_58_personBasketball——————————————
dvSave-2021_02_06_17_34_58_personBasketball , fps:2.792914218092888
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_17_45_17_personBasketball——————————————
dvSave-2021_02_06_17_45_17_personBasketball , fps:2.357332071700102
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_17_47_49_personBasketball——————————————
dvSave-2021_02_06_17_47_49_personBasketball , fps:4.212646238517635
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_17_49_51_personBasketball——————————————
dvSave-2021_02_06_17_49_51_personBasketball , fps:2.708500878802985
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_17_51_05_personBasketball——————————————
dvSave-2021_02_06_17_51_05_personBasketball , fps:2.7523244696867635
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_13_10_54_person——————————————
dvSave-2021_02_15_13_10_54_person , fps:2.0649676590057995
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_13_12_45_redcar——————————————
dvSave-2021_02_15_13_12_45_redcar , fps:2.1182711970807713
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_13_13_44_whitecar——————————————
dvSave-2021_02_15_13_13_44_whitecar , fps:2.1890747340776664
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_13_14_18_blackcar——————————————
dvSave-2021_02_15_13_14_18_blackcar , fps:2.396356529578665
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_10_23_05_basketall——————————————
dvSave-2021_02_15_10_23_05_basketall , fps:3.2699665227979833
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_10_23_05_boyhead——————————————
dvSave-2021_02_15_10_23_05_boyhead , fps:4.411197321207175
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_10_26_11_chicken——————————————
dvSave-2021_02_15_10_26_11_chicken , fps:4.953892924046702
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_12_44_27_chicken——————————————
dvSave-2021_02_15_12_44_27_chicken , fps:2.9310074438353255
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_13_01_16_Duck——————————————
dvSave-2021_02_15_13_01_16_Duck , fps:2.217863616982928
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_13_02_21_Chicken——————————————
dvSave-2021_02_15_13_02_21_Chicken , fps:2.5085457144292973
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_13_04_57_Duck——————————————
dvSave-2021_02_15_13_04_57_Duck , fps:2.317301703486951
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_13_05_43_Chicken——————————————
dvSave-2021_02_15_13_05_43_Chicken , fps:2.5595020999496216
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_14_16_56_01_house——————————————
dvSave-2021_02_14_16_56_01_house , fps:3.638524693071545
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_14_17_00_48——————————————
dvSave-2021_02_14_17_00_48 , fps:3.0263169285408242
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_10_12_19_basketball——————————————
dvSave-2021_02_15_10_12_19_basketball , fps:3.12635265116909
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_10_14_18_chicken——————————————
dvSave-2021_02_15_10_14_18_chicken , fps:5.469132803536085
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_12_13_43_54——————————————
dvSave-2021_02_12_13_43_54 , fps:2.241752423316839
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_12_13_46_18——————————————
dvSave-2021_02_12_13_46_18 , fps:2.6998803543373637
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_12_13_56_29——————————————
dvSave-2021_02_12_13_56_29 , fps:2.351792244173485
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_14_16_21_40——————————————
dvSave-2021_02_14_16_21_40 , fps:3.3272534111043606
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_10_17_16_paperClips——————————————
dvSave-2021_02_06_10_17_16_paperClips , fps:2.099213435356642
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_15_08_41_flag——————————————
dvSave-2021_02_06_15_08_41_flag , fps:2.8293356079024896
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_15_12_44_car——————————————
dvSave-2021_02_06_15_12_44_car , fps:2.11073731243203
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_15_14_26_blackcar——————————————
dvSave-2021_02_06_15_14_26_blackcar , fps:3.072238274265498
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_08_21_05_56_motor——————————————
dvSave-2021_02_08_21_05_56_motor , fps:2.794295162373211
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_08_21_07_52——————————————
dvSave-2021_02_08_21_07_52 , fps:2.502621427916738
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_12_13_38_26——————————————
dvSave-2021_02_12_13_38_26 , fps:2.7427893901647544
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_12_13_39_56——————————————
dvSave-2021_02_12_13_39_56 , fps:3.2564574881558994
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_09_58_27_DigitAI——————————————
dvSave-2021_02_06_09_58_27_DigitAI , fps:2.147416777741525
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_10_03_17_GreenPlant——————————————
dvSave-2021_02_06_10_03_17_GreenPlant , fps:2.503558789138599
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_10_05_38_phone——————————————
dvSave-2021_02_06_10_05_38_phone , fps:3.0194315052896776
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_06_10_09_04_bottle——————————————
dvSave-2021_02_06_10_09_04_bottle , fps:2.7636182565732432
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_13_24_03_girlhead——————————————
dvSave-2021_02_15_13_24_03_girlhead , fps:2.8505381362073923
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_13_24_49_girlhead——————————————
dvSave-2021_02_15_13_24_49_girlhead , fps:2.5145612270959425
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_13_27_20_bottle——————————————
dvSave-2021_02_15_13_27_20_bottle , fps:2.9597748422795824
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_13_28_20_cash——————————————
dvSave-2021_02_15_13_28_20_cash , fps:3.6993845501724554
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_12_45_02_Duck——————————————
dvSave-2021_02_15_12_45_02_Duck , fps:3.962225876419113
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_12_53_54_personHead——————————————
dvSave-2021_02_15_12_53_54_personHead , fps:4.055592808042287
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_16_17_38_25——————————————
dvSave-2021_02_16_17_38_25 , fps:3.200205628132136
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_16_17_42_50——————————————
dvSave-2021_02_16_17_42_50 , fps:4.198774893815552
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_14_16_22_06——————————————
dvSave-2021_02_14_16_22_06 , fps:3.746561850285315
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_14_16_53_15_flag——————————————
dvSave-2021_02_14_16_53_15_flag , fps:4.304859390407136
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0070——————————————
video_0070 , fps:5.456447164801351
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0073——————————————
video_0073 , fps:5.252577291062693
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0005——————————————
video_0005 , fps:3.4303541290066364
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0008——————————————
video_0008 , fps:3.3891169915415684
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0015——————————————
video_0015 , fps:4.069599668657785
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0018——————————————
video_0018 , fps:4.332204530094746
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_13_08_12_blackcar——————————————
dvSave-2021_02_15_13_08_12_blackcar , fps:2.8256490444799485
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_13_09_09_person——————————————
dvSave-2021_02_15_13_09_09_person , fps:3.4143114832684085
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0054——————————————
video_0054 , fps:4.262859680275538
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0056——————————————
video_0056 , fps:6.808767767763485
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_10_22_23_basketball——————————————
dvSave-2021_02_15_10_22_23_basketball , fps:3.595493869721401
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_10_22_23_boyhead——————————————
dvSave-2021_02_15_10_22_23_boyhead , fps:4.177709524480804
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0029——————————————
video_0029 , fps:5.71548499894405
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0032——————————————
video_0032 , fps:6.721668900634514
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: tennis_long_003——————————————
tennis_long_003 , fps:4.979334063552156
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: tennis_long_004——————————————
tennis_long_004 , fps:5.120787282557172
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: tennis_long_005——————————————
tennis_long_005 , fps:6.371455350439163
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: tennis_long_006——————————————
tennis_long_006 , fps:8.08441028598408
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_16_17_12_18——————————————
dvSave-2021_02_16_17_12_18 , fps:3.964559866555615
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_16_17_15_53——————————————
dvSave-2021_02_16_17_15_53 , fps:5.880255460940299
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_16_17_20_20——————————————
dvSave-2021_02_16_17_20_20 , fps:7.52402155376487
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_16_17_23_10——————————————
dvSave-2021_02_16_17_23_10 , fps:9.510953528691875
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0076——————————————
video_0076 , fps:4.286840908984083
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0079——————————————
video_0079 , fps:7.743896675477971
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0021——————————————
video_0021 , fps:6.700173855676422
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0026——————————————
video_0026 , fps:9.4784725865382
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dydrant_001——————————————
dydrant_001 , fps:4.627366493550821
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: roadLight_001——————————————
roadLight_001 , fps:5.208160158309092
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: tennis_long_001——————————————
tennis_long_001 , fps:7.664291677698512
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: tennis_long_002——————————————
tennis_long_002 , fps:12.019621431459356
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_23_51_36——————————————
dvSave-2021_02_15_23_51_36 , fps:4.444506029553768
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_23_54_17——————————————
dvSave-2021_02_15_23_54_17 , fps:4.516558708842863
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_15_23_56_17——————————————
dvSave-2021_02_15_23_56_17 , fps:6.4587484334114675
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_16_17_07_38——————————————
dvSave-2021_02_16_17_07_38 , fps:12.832460966397983
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0039——————————————
video_0039 , fps:10.330085989390739
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0045——————————————
video_0045 , fps:10.36563969725984
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0049——————————————
video_0049 , fps:11.42436460455213
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0050——————————————
video_0050 , fps:16.046580858975755
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_16_17_29_37——————————————
dvSave-2021_02_16_17_29_37 , fps:17.34895160441595
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: dvSave-2021_02_16_17_34_11——————————————
dvSave-2021_02_16_17_34_11 , fps:18.142144954156112
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0058——————————————
video_0058 , fps:8.99072564381166
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0060——————————————
video_0060 , fps:14.33415755256883
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0064——————————————
video_0064 , fps:20.366808539669105
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_ep0300.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 1e-05, 'EPOCH': 80, 'LR_DROP_EPOCH': 48, 'BATCH_SIZE': 32, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'AUXILIARY_WEIGHT': 0.5, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'WARMUP_EPOCHS': 0, 'FIX_BN': False, 'SAVE_EPOCH_INTERVAL': 1, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'TYPE': 'full', 'SCHEDULER': {'TYPE': 'step', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-06}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 10000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 56}}
——————————Process sequence: video_0067——————————————
video_0067 , fps:18.118043789147308
Totally cost 1h 31m 30s!
