# ViPT (Vision Prompt Tracking) Development Guide

> 🚀 **重要**: 请先阅读 [copilot-workflow-instructions.md](./copilot-workflow-instructions.md) 了解结构化的工作流程，再参考本文档的技术细节。

## Project Overview

ViPT is a multi-modal vision tracking framework that combines RGB images with event camera data using dual event representations (event images + voxel grids) through a prompt learning paradigm. The core innovation is a collaborative evolution mechanism between spatial semantic prompts and dynamic spatiotemporal prompts.

## Architecture

### Core Components
- **RGB Backbone**: OSTrack-based transformer frozen during training (prompt tuning)
- **Dual Event Branches**: 
  - Event images (spatial semantic prompts) via 2D patches
  - Event voxels (dynamic spatiotemporal prompts) via sparse 3D convolutions
- **Fusion Mechanisms**: 
  - Scene-adaptive gated injection 
  - Collaborative prompt evolution via cross-attention

### Key Directories
```
lib/models/vipt/           # Core model implementations
├── ostrack_prompt.py      # Main ViPTrack model with dual prompts
├── vit_ce_prompt.py       # Vision transformer with candidate elimination + prompts
└── vit_prompt.py          # Base transformer with prompt injection

lib/train/dataset/         # Multi-modal dataset loaders
├── coesot.py             # COESOT dataset (RGB + Event)
├── visevent.py           # VisEvent dataset 
└── felt.py               # FELT dataset

experiments/vipt/          # Training configurations per dataset/modality
scripts/event_to_frame_universal.py  # Event-to-image conversion
scripts/voxel/             # Event-to-voxel conversion utilities
RGBE_workspace/            # Testing infrastructure
```

## Development Workflows

### Training Pipeline
```bash
# Multi-GPU training with specific config
python tracking/train.py --script vipt --config deep_rgbe --save_dir ./output --mode multiple --nproc_per_node 4

# Key training script: scripts/sh/train_vipt.sh
# Configs define: model architecture, datasets, loss weights, prompt types
```

### Testing Pipeline
```bash
# Multi-threaded evaluation on specific dataset
nohup python ./RGBE_workspace/test_rgbe_mgpus.py --script_name vipt --yaml_name coesot4 --dataset COESOT --threads 4 --epoch 60 > test4.log 2>&1 &
```

### Data Processing
- **Event Images**: Time-window-based event aggregation with polarity encoding
- **Event Voxels**: 3D spatiotemporal discretization preserving temporal dynamics
- **Dataset Structure**: Each sequence has `_aps/` (RGB), `_dvs/` (event images), `_voxel_npy/` (voxel data)

## Configuration Patterns

### YAML Structure (experiments/vipt/*.yaml)
```yaml
MODEL:
  BACKBONE:
    TYPE: vit_base_patch16_224_ce_prompt  # Transformer + candidate elimination + prompts
    CE_LOC: [3, 6, 9]                     # Candidate elimination layers
  TRAIN:
    PROMPT:
      TYPE: vipt_deep                     # Dual prompt type (deep injection)
```

### Prompt Types
- `vipt_deep`: Deep prompt injection at multiple transformer layers
- `dual_prompt_*`: Configurations for dual-branch prompt evolution

## Key Implementation Details

### Model Initialization
- RGB backbone pretrained weights loaded from `./pretrained/OSTrack_*.pth.tar`
- Only prompt-related parameters are trainable (prompt tuning paradigm)
- Event branches initialized from scratch

### Multi-Modal Data Loading
```python
# Template signature in dataset classes
def __getitem__(self, index):
    # Returns: template_rgb, search_rgb, template_event, search_event, template_voxel, search_voxel, bbox
```

### Loss Computation
- Primary: IoU + L1 bounding box regression losses
- Event prompts contribute via gated injection, no separate event losses
- Candidate elimination (CE) mechanism reduces computational cost

## Testing and Evaluation

### Multi-Dataset Support
- **COESOT**: Large-scale RGB-Event tracking
- **VisEvent**: Large-scale Visible-Event benchmark
- **FELT**: Long-term Frame-Event tracking
- **FE108**: Gray Frame-Event tracking(with only challenges of low-light (LL), high dynamic range (HDR), fast motion with and without motion blur)

### Evaluation Scripts
- Use `RGBE_workspace/test_rgbe_mgpus.py` for multi-GPU testing
- Results saved in `RGBE_workspace/results/{DATASET}/`
- Supports VOT, OTB-style evaluation protocols

## Development Conventions

### File Naming
- Event images: `{seq_name}_dvs/*.bmp(png)`
- RGB images: `{seq_name}_aps/*.bmp(png|jpg)` 
- Voxel data: `{seq_name}_voxel_npy/*.npz`

### Logging
- Training logs: `output/logs/vipt-{config}.log`
- Loss visualization: `python scripts/loss.py adapter.log --field val`

### Branch Strategy
- Current branch: `voxel` (active development)
- Main branch: `main` (stable releases)

## Critical Dependencies
- Event data processing requires `aedat` format support
- Sparse 3D convolutions for voxel processing
- Multi-GPU training via `torchrun`
- Dataset paths configured in `lib/train/admin/local.py`

When working on this codebase, focus on the dual-prompt evolution mechanism and multi-modal data synchronization. Event-RGB alignment is critical for effective prompt generation.
